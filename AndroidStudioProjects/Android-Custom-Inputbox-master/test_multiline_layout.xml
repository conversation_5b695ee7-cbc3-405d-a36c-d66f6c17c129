<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 测试hint显示的单行输入框 -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/singleLineWithHint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:hint="请输入用户名"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#CCCCCC"
        app:edt_setCornerRadius="8dp"
        app:edt_setStrokeWidth="1dp"
        app:edt_setBackgroundColor="#FFFFFF"
        app:edt_multiLine="false"
        app:edt_showCharCount="true"
        app:edt_maxCharCount="20" />

    <!-- 测试动态右侧空间的单行输入框（短字数统计） -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/singleLineShortCount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:hint="短字数统计"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#CCCCCC"
        app:edt_setCornerRadius="8dp"
        app:edt_setStrokeWidth="1dp"
        app:edt_setBackgroundColor="#FFFFFF"
        app:edt_multiLine="false"
        app:edt_showCharCount="true"
        app:edt_maxCharCount="10" />

    <!-- 测试动态右侧空间的单行输入框（长字数统计） -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/singleLineLongCount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:hint="长字数统计"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#CCCCCC"
        app:edt_setCornerRadius="8dp"
        app:edt_setStrokeWidth="1dp"
        app:edt_setBackgroundColor="#FFFFFF"
        app:edt_multiLine="false"
        app:edt_showCharCount="true"
        app:edt_maxCharCount="1000" />

    <!-- 测试多行输入框 -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/multiLineWithHint"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:layout_margin="8dp"
        android:hint="请输入多行文本内容"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#CCCCCC"
        app:edt_setCornerRadius="8dp"
        app:edt_setStrokeWidth="1dp"
        app:edt_setBackgroundColor="#FFFFFF"
        app:edt_multiLine="true"
        app:edt_showCharCount="true"
        app:edt_maxCharCount="200" />

    <!-- 测试不显示字数统计的输入框 -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/noCharCount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:hint="不显示字数统计"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#CCCCCC"
        app:edt_setCornerRadius="8dp"
        app:edt_setStrokeWidth="1dp"
        app:edt_setBackgroundColor="#FFFFFF"
        app:edt_multiLine="false"
        app:edt_showCharCount="false" />

</LinearLayout>
