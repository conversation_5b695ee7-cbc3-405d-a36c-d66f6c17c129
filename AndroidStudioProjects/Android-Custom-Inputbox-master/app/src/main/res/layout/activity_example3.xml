<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="CustomInputBox3 Demo (Java Version)"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginBottom="24dp" />
    <!-- Regular EditText with border -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText0"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="default input"
        android:layout_marginBottom="16dp" />
    <!-- Regular EditText with border -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Regular input with border"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#2196F3"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

    <!-- Password EditText with show/hide functionality -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Password input"
        android:inputType="textPassword"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#FF5722"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

    <!-- EditText with clear button -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText3"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Input with clear button"
        app:edt_setClearIconVisible="true"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#4CAF50"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

    <!-- EditText with custom drawable and click listener -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText4"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Input with custom drawable"
        android:drawableEnd="@android:drawable/ic_menu_search"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#9C27B0"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

    <!-- Phone number input with pattern -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText5"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Phone number"
        android:inputType="phone"
        app:edt_pattern="(###) ###-####"
        app:edt_specialChar="#"
        app:edt_showPatternAsHint="true"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#FF9800"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

    <!-- EditText with prefix -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText6"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:hint="Amount"
        app:edt_setPrefix="$"
        app:edt_setPrefixTextColor="#E91E63"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#607D8B"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

        <!-- 新的多行输入框 - 多行模式 -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText7"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:hint="请输入评论内容..."
        android:inputType="textMultiLine"
        app:edt_multiLine="true"
        app:edt_maxCharCount="60"
        app:edt_charCountTextColor="#666666"
        app:edt_charCountTextSize="12sp"
        app:edt_showCharCount="true"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#FF9800"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

    <!-- 新的多行输入框 - 单行模式 -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText8"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:hint="请输入简介..."
        android:inputType="text"
        app:edt_multiLine="false"
        app:edt_maxCharCount="100"
        app:edt_charCountTextColor="#2196F3"
        app:edt_charCountTextSize="14sp"
        app:edt_showCharCount="true"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#4CAF50"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

    <!-- 字数统计隐藏的输入框 -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText9"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:hint="字数统计已隐藏的输入框..."
        android:inputType="textMultiLine"
        app:edt_multiLine="true"
        app:edt_maxCharCount="50"
        app:edt_showCharCount="false"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#9C27B0"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

    <!-- 测试单行输入框 -->
    <com.wkb.custominputbox3.MultiLineEditText
        android:id="@+id/editText10"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:hint="单行输入测试..."
        android:inputType="text"
        app:edt_multiLine="false"
        app:edt_maxCharCount="30"
        app:edt_charCountTextColor="#E91E63"
        app:edt_charCountTextSize="12sp"
        app:edt_showCharCount="true"
        app:edt_setBorderView="true"
        app:edt_setBorderColor="#607D8B"
        app:edt_setCornerRadius="8dp"
        android:layout_marginBottom="16dp" />

    </LinearLayout>
</ScrollView> 