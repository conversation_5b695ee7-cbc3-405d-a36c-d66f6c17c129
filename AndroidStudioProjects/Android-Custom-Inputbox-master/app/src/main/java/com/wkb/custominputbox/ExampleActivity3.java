package com.wkb.custominputbox;

import android.os.Bundle;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.wkb.custominputbox3.MultiLineEditText;
import com.wkb.custominputbox3.utils.DrawableClickListener;

public class ExampleActivity3 extends AppCompatActivity {

    private MultiLineEditText editText1, editText2, editText3, editText4, editText7, editText8, editText9, editText10;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_example3);

        initViews();
        setupListeners();
    }

    private void initViews() {
        editText1 = findViewById(R.id.editText1);
        editText2 = findViewById(R.id.editText2);
        editText3 = findViewById(R.id.editText3);
        editText4 = findViewById(R.id.editText4);
        editText7 = findViewById(R.id.editText7);
        editText8 = findViewById(R.id.editText8);
        editText9 = findViewById(R.id.editText9);
        editText10 = findViewById(R.id.editText10);
    }

    private void setupListeners() {
        // Set drawable click listener for editText4
        editText4.setDrawableClickListener(new DrawableClickListener() {
            @Override
            public void onRightClick() {
                Toast.makeText(ExampleActivity3.this, "Right drawable clicked!", Toast.LENGTH_SHORT).show();
            }
        });
        
        // 测试新的多行输入框功能
        editText7.addTextChangedListener(new android.text.TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(android.text.Editable s) {
                if (s.length() > 60) {
                    Toast.makeText(ExampleActivity3.this, "多行输入框已达到最大字数限制", Toast.LENGTH_SHORT).show();
                }
            }
        });
        
        editText8.addTextChangedListener(new android.text.TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(android.text.Editable s) {
                if (s.length() > 100) {
                    Toast.makeText(ExampleActivity3.this, "单行输入框已达到最大字数限制", Toast.LENGTH_SHORT).show();
                }
            }
        });
        
        // 测试字数统计显示控制
        editText9.addTextChangedListener(new android.text.TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(android.text.Editable s) {
                if (s.length() > 50) {
                    Toast.makeText(ExampleActivity3.this, "字数统计隐藏的输入框已达到限制", Toast.LENGTH_SHORT).show();
                }
            }
        });
        
        // 测试单行输入框
        editText10.addTextChangedListener(new android.text.TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(android.text.Editable s) {
                if (s.length() > 30) {
                    Toast.makeText(ExampleActivity3.this, "单行输入框已达到限制", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }
} 