package com.wkb.custominputbox

import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.wkb.custominputbox.databinding.ActivityMainBinding
import com.wkb.custominputbox2.utils.AmountInput
import com.wkb.custominputbox2.utils.DrawableClickListener

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        /**
         * Right click event of drawable using DrawableClickListener.
         */
        binding.edtUserName.setDrawableClickListener(object : DrawableClickListener {

            override fun onRightClick() {
                Toast.makeText(this@MainActivity, "Right Drawable click", Toast.LENGTH_SHORT).show()
            }
        })

        /**
         * set amount format in input box with specific device language.
         */

        AmountInput(this, binding.mainView, binding.etAmount)

        /**
         * <PERSON><PERSON> to navigate to Java version demo
         */
        binding.btnJavaVersion.setOnClickListener {
            val intent = android.content.Intent(this, ExampleActivity3::class.java)
            startActivity(intent)
        }

    }
}
