<component name="libraryTable">
  <library name="Gradle: com.intuit.ssp:ssp-android:1.0.6@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/641e0e345f3db790187f5f79d6e5a5df/transformed/ssp-android-1.0.6/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/641e0e345f3db790187f5f79d6e5a5df/transformed/ssp-android-1.0.6/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/641e0e345f3db790187f5f79d6e5a5df/transformed/ssp-android-1.0.6/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>