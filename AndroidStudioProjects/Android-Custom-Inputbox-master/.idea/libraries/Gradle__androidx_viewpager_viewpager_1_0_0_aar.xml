<component name="libraryTable">
  <library name="Gradle: androidx.viewpager:viewpager:1.0.0@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/b1bb79a3d7882f1b30506dd95ac777b6/transformed/viewpager-1.0.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/b1bb79a3d7882f1b30506dd95ac777b6/transformed/viewpager-1.0.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.viewpager/viewpager/1.0.0/db045f92188b9d247d5f556866f8861ab68528f0/viewpager-1.0.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>