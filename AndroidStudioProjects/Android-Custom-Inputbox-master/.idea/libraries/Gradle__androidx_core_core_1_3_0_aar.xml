<component name="libraryTable">
  <library name="Gradle: androidx.core:core:1.3.0@aar" external-system-id="GRADLE">
    <ANNOTATIONS>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/408e66d1a06fdc9157e79b7bf2533818/transformed/core-1.3.0/annotations.zip!/" />
    </ANNOTATIONS>
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/408e66d1a06fdc9157e79b7bf2533818/transformed/core-1.3.0/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/408e66d1a06fdc9157e79b7bf2533818/transformed/core-1.3.0/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/408e66d1a06fdc9157e79b7bf2533818/transformed/core-1.3.0/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/androidx.core/core/1.3.0/1acbde5fef5f77f6920e9f9a5dbe1ca217a64bc0/core-1.3.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>