<component name="libraryTable">
  <library name="Gradle: org.jetbrains.kotlin:kotlin-stdlib-common:1.3.71" type="kotlin.common" external-system-id="GRADLE">
    <properties groupId="org.jetbrains.kotlin" artifactId="kotlin-stdlib-common" version="1.3.71" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-common/1.3.71/e71c3fef58e26affeb03d675e91fd8abdd44aa7b/kotlin-stdlib-common-1.3.71.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-common/1.3.71/2aac421f5966889ccff3a970206dbb5537e5be84/kotlin-stdlib-common-1.3.71-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-common/1.3.71/bc2d3018bbb572e087e0afee6bedfba35eba125f/kotlin-stdlib-common-1.3.71-sources.jar!/" />
    </SOURCES>
  </library>
</component>