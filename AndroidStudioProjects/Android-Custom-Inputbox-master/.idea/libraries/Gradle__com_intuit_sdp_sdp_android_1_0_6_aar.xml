<component name="libraryTable">
  <library name="Gradle: com.intuit.sdp:sdp-android:1.0.6@aar" external-system-id="GRADLE">
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/transforms-3/735d63fcc588346fc37dbf72eccbc0d2/transformed/sdp-android-1.0.6/jars/classes.jar!/" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/735d63fcc588346fc37dbf72eccbc0d2/transformed/sdp-android-1.0.6/res" />
      <root url="file://$USER_HOME$/.gradle/caches/transforms-3/735d63fcc588346fc37dbf72eccbc0d2/transformed/sdp-android-1.0.6/AndroidManifest.xml" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>