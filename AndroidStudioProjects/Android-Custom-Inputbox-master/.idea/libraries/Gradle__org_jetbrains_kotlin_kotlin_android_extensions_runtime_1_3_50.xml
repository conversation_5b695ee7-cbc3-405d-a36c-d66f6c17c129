<component name="libraryTable">
  <library name="Gradle: org.jetbrains.kotlin:kotlin-android-extensions-runtime:1.3.50" type="java-imported" external-system-id="GRADLE">
    <properties groupId="org.jetbrains.kotlin" artifactId="kotlin-android-extensions-runtime" version="1.3.50" />
    <CLASSES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-android-extensions-runtime/1.3.50/bec16087637a7cafe54894e73d38037977cb30d2/kotlin-android-extensions-runtime-1.3.50.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-android-extensions-runtime/1.3.50/5bc5def01933bfd085d7a228cc7a1196a9dde673/kotlin-android-extensions-runtime-1.3.50-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$USER_HOME$/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-android-extensions-runtime/1.3.50/c051c7ca286237fc0a4e14c9a3e487672a373a48/kotlin-android-extensions-runtime-1.3.50-sources.jar!/" />
    </SOURCES>
  </library>
</component>