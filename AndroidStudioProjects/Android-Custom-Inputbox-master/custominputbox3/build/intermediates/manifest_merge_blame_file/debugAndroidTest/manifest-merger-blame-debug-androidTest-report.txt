1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wkb.custominputbox3.test" >
4
5    <uses-sdk
5-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:5:5-74
6        android:minSdkVersion="16"
6-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:5:15-41
7        android:targetSdkVersion="33" />
7-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:5:42-71
8
9    <instrumentation
9-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:11:5-15:78
10        android:name="androidx.test.runner.AndroidJUnitRunner"
10-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:11:22-76
11        android:functionalTest="false"
11-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:14:22-52
12        android:handleProfiling="false"
12-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:13:22-53
13        android:label="Tests for com.wkb.custominputbox3.test"
13-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:15:22-76
14        android:targetPackage="com.wkb.custominputbox3.test" />
14-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:12:22-74
15
16    <uses-permission android:name="android.permission.REORDER_TASKS" />
16-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:24:5-72
16-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:24:22-69
17
18    <queries>
18-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/transforms-3/5f64260eb5125a9d523972963a64747a/transformed/runner-1.5.2/AndroidManifest.xml:24:5-28:15
19        <package android:name="androidx.test.orchestrator" />
19-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/transforms-3/5f64260eb5125a9d523972963a64747a/transformed/runner-1.5.2/AndroidManifest.xml:25:9-62
19-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/transforms-3/5f64260eb5125a9d523972963a64747a/transformed/runner-1.5.2/AndroidManifest.xml:25:18-59
20        <package android:name="androidx.test.services" />
20-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/transforms-3/5f64260eb5125a9d523972963a64747a/transformed/runner-1.5.2/AndroidManifest.xml:26:9-58
20-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/transforms-3/5f64260eb5125a9d523972963a64747a/transformed/runner-1.5.2/AndroidManifest.xml:26:18-55
21        <package android:name="com.google.android.apps.common.testing.services" />
21-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/transforms-3/5f64260eb5125a9d523972963a64747a/transformed/runner-1.5.2/AndroidManifest.xml:27:9-83
21-->[androidx.test:runner:1.5.2] /Users/<USER>/.gradle/caches/transforms-3/5f64260eb5125a9d523972963a64747a/transformed/runner-1.5.2/AndroidManifest.xml:27:18-80
22    </queries>
23
24    <permission
24-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
25        android:name="com.wkb.custominputbox3.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
25-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
26        android:protectionLevel="signature" />
26-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
27
28    <uses-permission android:name="com.wkb.custominputbox3.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
28-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
28-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
29
30    <application
30-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:7:5-9:19
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
32        android:debuggable="true" >
33        <uses-library android:name="android.test.runner" />
33-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:8:9-60
33-->/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/intermediates/tmp/manifest/androidTest/debug/tempFile1ProcessTestManifest9842338969743965589.xml:8:23-57
34
35        <activity
35-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:27:9-34:20
36            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
36-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:28:13-99
37            android:exported="true"
37-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:29:13-36
38            android:theme="@style/WhiteBackgroundTheme" >
38-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:30:13-56
39            <intent-filter android:priority="-100" >
39-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
39-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
40                <category android:name="android.intent.category.LAUNCHER" />
40-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
40-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
41            </intent-filter>
42        </activity>
43        <activity
43-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:35:9-42:20
44            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
44-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:36:13-95
45            android:exported="true"
45-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:37:13-36
46            android:theme="@style/WhiteBackgroundTheme" >
46-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:38:13-56
47            <intent-filter android:priority="-100" >
47-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
47-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
48                <category android:name="android.intent.category.LAUNCHER" />
48-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
48-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
49            </intent-filter>
50        </activity>
51        <activity
51-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:43:9-50:20
52            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
52-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:44:13-103
53            android:exported="true"
53-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:45:13-36
54            android:theme="@style/WhiteBackgroundDialogTheme" >
54-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:46:13-62
55            <intent-filter android:priority="-100" >
55-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:13-33:29
55-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:31:28-51
56                <category android:name="android.intent.category.LAUNCHER" />
56-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:17-77
56-->[androidx.test:core:1.5.0] /Users/<USER>/.gradle/caches/transforms-3/b2ab149633869b6e74cda5cebb904588/transformed/jetified-core-1.5.0/AndroidManifest.xml:32:27-74
57            </intent-filter>
58        </activity>
59
60        <provider
60-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/9eaa5499e5917bf161e9e1c1138b7d31/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
61            android:name="androidx.startup.InitializationProvider"
61-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/9eaa5499e5917bf161e9e1c1138b7d31/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
62            android:authorities="com.wkb.custominputbox3.test.androidx-startup"
62-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/9eaa5499e5917bf161e9e1c1138b7d31/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
63            android:exported="false" >
63-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/9eaa5499e5917bf161e9e1c1138b7d31/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
64            <meta-data
64-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/9eaa5499e5917bf161e9e1c1138b7d31/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
65                android:name="androidx.emoji2.text.EmojiCompatInitializer"
65-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/9eaa5499e5917bf161e9e1c1138b7d31/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
66                android:value="androidx.startup" />
66-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/transforms-3/9eaa5499e5917bf161e9e1c1138b7d31/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
67            <meta-data
67-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/afbe6f79812804f6bb7e1730342d066f/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:31:13-33:52
68                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
68-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/afbe6f79812804f6bb7e1730342d066f/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:32:17-78
69                android:value="androidx.startup" />
69-->[androidx.lifecycle:lifecycle-process:2.4.1] /Users/<USER>/.gradle/caches/transforms-3/afbe6f79812804f6bb7e1730342d066f/transformed/jetified-lifecycle-process-2.4.1/AndroidManifest.xml:33:17-49
70        </provider>
71    </application>
72
73</manifest>
