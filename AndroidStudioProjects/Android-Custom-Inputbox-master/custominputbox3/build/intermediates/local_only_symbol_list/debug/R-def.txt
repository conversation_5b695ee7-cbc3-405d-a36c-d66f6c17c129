R_DEF: Internal format may change without notice
local
attr? edt_background
attr? edt_charCountTextColor
attr? edt_charCountTextSize
attr? edt_clearIconTint
attr? edt_cursor
attr? edt_hideShowPasswordIconTint
attr? edt_maxCharCount
attr? edt_minLength
attr? edt_multiLine
attr? edt_pattern
attr? edt_regexp
attr? edt_setBackgroundColor
attr? edt_setBorderColor
attr? edt_setBorderView
attr? edt_setClearIconVisible
attr? edt_setCornerRadius
attr? edt_setFont
attr? edt_setPrefix
attr? edt_setPrefixTextColor
attr? edt_setStrokeWidth
attr? edt_showCharCount
attr? edt_showPatternAsHint
attr? edt_specialChar
attr keyHeight
attr? keyPadding
attr keyWidth
attr keyboardType
attr? layout
attr? leftAuxBtnBackground
attr? leftAuxBtnIcon
attr? leftAuxTextColor
attr? numberKeyBackground
attr? numberKeyTextColor
attr? rightAuxBtnBackground
attr? rightAuxBtnIcon
drawable ic_backspace
drawable ic_backspace_normal
drawable ic_backspace_pressed
drawable ic_check_circle
drawable ic_check_circle_normal
drawable ic_check_circle_pressed
drawable ic_comma
drawable ic_comma_normal
drawable ic_comma_pressed
drawable ic_fingerprint
drawable ic_fingerprint_normal
drawable ic_fingerprint_pressed
drawable ic_visibility_off
drawable ic_visibility_on
drawable key_bg
drawable key_bg_normal
drawable key_bg_pressed
drawable key_bg_transparent
drawable key_text_color
drawable key_text_color2
font roboto_black
font roboto_bold
font roboto_medium
font roboto_regular
id custom
id decimal
id fingerprint
id four_columns
id integer
id match_parent
string app_name
string comma
string dialog_btn_ok
string dot
string eight
string error_min_value
string error_regex
string five
string four
string minus
string nine
string ok
string one
string robotoBold
string robotoRegular
string seven
string six
string something_wrong
string str_amount
string str_credit_card
string str_password
string str_phone_number
string str_title
string str_username
string three
string two
string zero
style key
style keyContainer
style keyNoBg
styleable CustomEditText android:inputType android:padding android:paddingLeft android:paddingRight android:paddingTop android:paddingBottom android:maxLength edt_cursor edt_setBackgroundColor edt_background edt_setBorderColor edt_setBorderView edt_setFont edt_setStrokeWidth edt_setCornerRadius edt_setClearIconVisible edt_clearIconTint edt_hideShowPasswordIconTint edt_setPrefix edt_setPrefixTextColor edt_minLength edt_regexp edt_pattern edt_specialChar edt_showPatternAsHint edt_multiLine edt_maxCharCount edt_charCountTextColor edt_charCountTextSize edt_showCharCount
styleable NumberKeyboard keyboardType keyWidth keyHeight keyPadding numberKeyBackground numberKeyTextColor leftAuxTextColor leftAuxBtnIcon leftAuxBtnBackground rightAuxBtnIcon rightAuxBtnBackground layout
