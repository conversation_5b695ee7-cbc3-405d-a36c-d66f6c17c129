{"logs": [{"outputFile": "com.wkb.custominputbox3-merged_res-20:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/7002a03739265e0a14b05d2e207153d0/transformed/jetified-savedstate-1.2.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "275", "startColumns": "4", "startOffsets": "17469", "endColumns": "53", "endOffsets": "17518"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "311,319,320,321,322,323,324,325,326,327,328,329,330,331,332,334,335,336,341,342,343,344,345,346,347,348,349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19899,20462,20498,20543,20577,20613,20705,20775,20810,20845,20881,20916,20950,20984,21039,21153,21189,21223,21426,21478,21552,21602,21671,21725,21775,21811,21845", "endLines": "311,319,320,321,322,323,324,325,326,327,328,329,330,331,332,334,335,339,341,342,343,344,345,346,347,348,349", "endColumns": "51,35,44,33,35,91,69,34,34,35,34,33,33,54,60,35,33,13,51,73,49,68,53,49,35,33,34", "endOffsets": "19946,20493,20538,20572,20608,20700,20770,20805,20840,20876,20911,20945,20979,21034,21095,21184,21218,21350,21473,21547,21597,21666,21720,21770,21806,21840,21875"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/91e8016f0a249a956def1658dacf6ac4/transformed/lifecycle-runtime-2.5.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "273", "startColumns": "4", "startOffsets": "17366", "endColumns": "42", "endOffsets": "17404"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/attrs.xml", "from": {"startLines": "-1,-1,-1,3", "startColumns": "-1,-1,-1,4", "startOffsets": "-1,-1,-1,56", "endLines": "-1,-1,-1,34", "endColumns": "-1,-1,-1,24", "endOffsets": "-1,-1,-1,1773"}, "to": {"startLines": "5,8,11,2575", "startColumns": "4,4,4,4", "startOffsets": "299,405,510,145769", "endLines": "7,10,17,2606", "endColumns": "11,11,11,24", "endOffsets": "400,505,761,147456"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/30c6f81231359517120429df1c780a80/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "310", "startColumns": "4", "startOffsets": "19816", "endColumns": "82", "endOffsets": "19894"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6740173314278d5dc47a04980597f4e3/transformed/lifecycle-viewmodel-2.5.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "276", "startColumns": "4", "startOffsets": "17523", "endColumns": "49", "endOffsets": "17568"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "18,29,30,43,44,67,68,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,250,251,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,282,312,313,314,315,316,317,318,340,1709,1710,1714,1715,1719,1863,1864", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,1485,1557,2605,2670,4089,4158,11076,11146,11214,11286,11356,11417,11491,12348,12409,12470,12532,12596,12658,12719,12787,12887,12947,13013,13086,13155,13212,13264,13779,13851,13927,13992,14051,14110,14170,14230,14290,14350,14410,14470,14530,14590,14650,14710,14769,14829,14889,14949,15009,15069,15129,15189,15249,15309,15369,15428,15488,15548,15607,15666,15725,15784,15843,16198,16233,16475,16530,16593,16648,16706,16764,16825,16888,16945,16996,17046,17107,17164,17230,17264,17299,17880,19951,20018,20090,20159,20228,20302,20374,21355,112206,112323,112524,112634,112835,124259,124331", "endLines": "18,29,30,43,44,67,68,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,250,251,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,282,312,313,314,315,316,317,318,340,1709,1713,1714,1718,1719,1863,1864", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "821,1552,1640,2665,2731,4153,4216,11141,11209,11281,11351,11412,11486,11559,12404,12465,12527,12591,12653,12714,12782,12882,12942,13008,13081,13150,13207,13259,13321,13846,13922,13987,14046,14105,14165,14225,14285,14345,14405,14465,14525,14585,14645,14705,14764,14824,14884,14944,15004,15064,15124,15184,15244,15304,15364,15423,15483,15543,15602,15661,15720,15779,15838,15897,16228,16263,16525,16588,16643,16701,16759,16820,16883,16940,16991,17041,17102,17159,17225,17259,17294,17329,17945,20013,20085,20154,20223,20297,20369,20457,21421,112318,112519,112629,112830,112959,124326,124393"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1865,1868,1872", "startColumns": "4,4,4", "startOffsets": "124398,124515,124667", "endLines": "1867,1871,1883", "endColumns": "12,12,12", "endOffsets": "124510,124662,125294"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/10721da65f20075e1e7ee43161f80a4f/transformed/appcompat-1.6.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,19,20,21,22,23,24,25,26,27,28,31,32,33,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,201,202,203,204,205,206,207,208,244,245,246,247,249,252,253,255,272,278,279,280,281,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,333,350,351,352,353,354,355,363,364,368,372,376,381,387,394,398,402,407,411,415,419,423,427,431,437,441,447,451,457,461,466,470,473,477,483,487,493,497,503,506,510,514,518,522,526,527,528,529,532,535,538,541,545,546,547,548,549,552,554,556,558,563,564,568,574,578,579,581,593,594,598,604,608,609,610,614,641,645,646,650,678,850,876,1047,1073,1104,1112,1118,1134,1156,1161,1166,1176,1185,1194,1198,1205,1224,1231,1232,1241,1244,1247,1251,1255,1259,1262,1263,1268,1273,1283,1288,1295,1301,1302,1305,1309,1314,1316,1318,1321,1324,1326,1330,1333,1340,1343,1346,1350,1352,1356,1358,1360,1362,1366,1374,1382,1394,1400,1409,1412,1423,1426,1427,1432,1433,1438,1507,1577,1578,1588,1597,1598,1600,1604,1607,1610,1613,1616,1619,1622,1625,1629,1632,1635,1638,1642,1645,1649,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1686,1687,1689,1690,1692,1694,1695,1697,1698,1699,1700,1701,1702,1704,1705,1706,1707,1708,1720,1722,1724,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1740,1741,1742,1743,1744,1745,1746,1748,1752,1756,1757,1758,1759,1760,1761,1765,1766,1767,1768,1770,1772,1774,1776,1778,1779,1780,1781,1783,1785,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1801,1802,1803,1804,1806,1808,1809,1811,1812,1814,1816,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1831,1832,1833,1834,1836,1837,1838,1839,1840,1842,1844,1846,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,826,867,922,984,1048,1118,1179,1254,1330,1407,1645,1730,1812,1888,1964,2041,2119,2225,2331,2410,2490,2547,2736,2810,2885,2950,3016,3076,3137,3209,3282,3349,3417,3476,3535,3594,3653,3712,3766,3820,3873,3927,3981,4035,4221,4295,4374,4447,4521,4592,4664,4736,4809,4866,4924,4997,5071,5145,5220,5292,5365,5435,5506,5566,5627,5696,5765,5835,5909,5985,6049,6126,6202,6279,6344,6413,6490,6565,6634,6702,6779,6845,6906,7003,7068,7137,7236,7307,7366,7424,7481,7540,7604,7675,7747,7819,7891,7963,8030,8098,8166,8225,8288,8352,8442,8533,8593,8659,8726,8792,8862,8926,8979,9046,9107,9174,9287,9345,9408,9473,9538,9613,9686,9758,9802,9849,9895,9944,10005,10066,10127,10189,10253,10317,10381,10446,10509,10569,10630,10696,10755,10815,10877,10948,11008,11564,11650,11737,11827,11914,12002,12084,12167,12257,13326,13378,13436,13481,13547,13611,13668,13725,15902,15959,16007,16056,16164,16268,16315,16429,17334,17637,17701,17763,17823,17950,18024,18094,18172,18226,18296,18381,18429,18475,18536,18599,18665,18729,18800,18863,18928,18992,19053,19114,19166,19239,19313,19382,19457,19531,19605,19746,21100,21880,21958,22048,22136,22232,22322,22904,22993,23240,23521,23773,24058,24451,24928,25150,25372,25648,25875,26105,26335,26565,26795,27022,27441,27667,28092,28322,28750,28969,29252,29460,29591,29818,30244,30469,30896,31117,31542,31662,31938,32239,32563,32854,33168,33305,33436,33541,33783,33950,34154,34362,34633,34745,34857,34962,35079,35293,35439,35579,35665,36013,36101,36347,36765,37014,37096,37194,37851,37951,38203,38627,38882,38976,39065,39302,41326,41568,41670,41923,44079,54760,56276,66971,68499,70256,70882,71302,72563,73828,74084,74320,74867,75361,75966,76164,76744,78112,78487,78605,79143,79300,79496,79769,80025,80195,80336,80400,80765,81132,81808,82072,82410,82763,82857,83043,83349,83611,83736,83863,84102,84313,84432,84625,84802,85257,85438,85560,85819,85932,86119,86221,86328,86457,86732,87240,87736,88613,88907,89477,89626,90358,90530,90614,90950,91042,91320,96551,101922,101984,102562,103146,103237,103350,103579,103739,103891,104062,104228,104397,104564,104727,104970,105140,105313,105484,105758,105957,106162,106492,106576,106672,106768,106866,106966,107068,107170,107272,107374,107476,107576,107672,107784,107913,108036,108167,108298,108396,108510,108604,108744,108878,108974,109086,109186,109302,109398,109510,109610,109750,109886,110050,110180,110338,110488,110629,110773,110908,111020,111170,111298,111426,111562,111694,111824,111954,112066,112964,113110,113254,113392,113458,113548,113624,113728,113818,113920,114028,114136,114236,114316,114408,114506,114616,114668,114746,114852,114944,115048,115158,115280,115443,115600,115680,115780,115870,115980,116070,116311,116405,116511,116603,116703,116815,116929,117045,117161,117255,117369,117481,117583,117703,117825,117907,118011,118131,118257,118355,118449,118537,118649,118765,118887,118999,119174,119290,119376,119468,119580,119704,119771,119897,119965,120093,120237,120365,120434,120529,120644,120757,120856,120965,121076,121187,121288,121393,121493,121623,121714,121837,121931,122043,122129,122233,122329,122417,122535,122639,122743,122869,122957,123065,123165,123255,123365,123449,123551,123635,123689,123753,123859,123945,124055,124139", "endLines": "2,3,4,19,20,21,22,23,24,25,26,27,28,31,32,33,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,201,202,203,204,205,206,207,208,244,245,246,247,249,252,253,255,272,278,279,280,281,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,333,350,351,352,353,354,362,363,367,371,375,380,386,393,397,401,406,410,414,418,422,426,430,436,440,446,450,456,460,465,469,472,476,482,486,492,496,502,505,509,513,517,521,525,526,527,528,531,534,537,540,544,545,546,547,548,551,553,555,557,562,563,567,573,577,578,580,592,593,597,603,607,608,609,613,640,644,645,649,677,849,875,1046,1072,1103,1111,1117,1133,1155,1160,1165,1175,1184,1193,1197,1204,1223,1230,1231,1240,1243,1246,1250,1254,1258,1261,1262,1267,1272,1282,1287,1294,1300,1301,1304,1308,1313,1315,1317,1320,1323,1325,1329,1332,1339,1342,1345,1349,1351,1355,1357,1359,1361,1365,1373,1381,1393,1399,1408,1411,1422,1425,1426,1431,1432,1437,1506,1576,1577,1587,1596,1597,1599,1603,1606,1609,1612,1615,1618,1621,1624,1628,1631,1634,1637,1641,1644,1648,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1685,1686,1688,1689,1691,1693,1694,1696,1697,1698,1699,1700,1701,1703,1704,1705,1706,1707,1708,1721,1723,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1739,1740,1741,1742,1743,1744,1745,1747,1751,1755,1756,1757,1758,1759,1760,1764,1765,1766,1767,1769,1771,1773,1775,1777,1778,1779,1780,1782,1784,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1800,1801,1802,1803,1805,1807,1808,1810,1811,1813,1815,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1830,1831,1832,1833,1835,1836,1837,1838,1839,1841,1843,1845,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,294,862,917,979,1043,1113,1174,1249,1325,1402,1480,1725,1807,1883,1959,2036,2114,2220,2326,2405,2485,2542,2600,2805,2880,2945,3011,3071,3132,3204,3277,3344,3412,3471,3530,3589,3648,3707,3761,3815,3868,3922,3976,4030,4084,4290,4369,4442,4516,4587,4659,4731,4804,4861,4919,4992,5066,5140,5215,5287,5360,5430,5501,5561,5622,5691,5760,5830,5904,5980,6044,6121,6197,6274,6339,6408,6485,6560,6629,6697,6774,6840,6901,6998,7063,7132,7231,7302,7361,7419,7476,7535,7599,7670,7742,7814,7886,7958,8025,8093,8161,8220,8283,8347,8437,8528,8588,8654,8721,8787,8857,8921,8974,9041,9102,9169,9282,9340,9403,9468,9533,9608,9681,9753,9797,9844,9890,9939,10000,10061,10122,10184,10248,10312,10376,10441,10504,10564,10625,10691,10750,10810,10872,10943,11003,11071,11645,11732,11822,11909,11997,12079,12162,12252,12343,13373,13431,13476,13542,13606,13663,13720,13774,15954,16002,16051,16102,16193,16310,16359,16470,17361,17696,17758,17818,17875,18019,18089,18167,18221,18291,18376,18424,18470,18531,18594,18660,18724,18795,18858,18923,18987,19048,19109,19161,19234,19308,19377,19452,19526,19600,19741,19811,21148,21953,22043,22131,22227,22317,22899,22988,23235,23516,23768,24053,24446,24923,25145,25367,25643,25870,26100,26330,26560,26790,27017,27436,27662,28087,28317,28745,28964,29247,29455,29586,29813,30239,30464,30891,31112,31537,31657,31933,32234,32558,32849,33163,33300,33431,33536,33778,33945,34149,34357,34628,34740,34852,34957,35074,35288,35434,35574,35660,36008,36096,36342,36760,37009,37091,37189,37846,37946,38198,38622,38877,38971,39060,39297,41321,41563,41665,41918,44074,54755,56271,66966,68494,70251,70877,71297,72558,73823,74079,74315,74862,75356,75961,76159,76739,78107,78482,78600,79138,79295,79491,79764,80020,80190,80331,80395,80760,81127,81803,82067,82405,82758,82852,83038,83344,83606,83731,83858,84097,84308,84427,84620,84797,85252,85433,85555,85814,85927,86114,86216,86323,86452,86727,87235,87731,88608,88902,89472,89621,90353,90525,90609,90945,91037,91315,96546,101917,101979,102557,103141,103232,103345,103574,103734,103886,104057,104223,104392,104559,104722,104965,105135,105308,105479,105753,105952,106157,106487,106571,106667,106763,106861,106961,107063,107165,107267,107369,107471,107571,107667,107779,107908,108031,108162,108293,108391,108505,108599,108739,108873,108969,109081,109181,109297,109393,109505,109605,109745,109881,110045,110175,110333,110483,110624,110768,110903,111015,111165,111293,111421,111557,111689,111819,111949,112061,112201,113105,113249,113387,113453,113543,113619,113723,113813,113915,114023,114131,114231,114311,114403,114501,114611,114663,114741,114847,114939,115043,115153,115275,115438,115595,115675,115775,115865,115975,116065,116306,116400,116506,116598,116698,116810,116924,117040,117156,117250,117364,117476,117578,117698,117820,117902,118006,118126,118252,118350,118444,118532,118644,118760,118882,118994,119169,119285,119371,119463,119575,119699,119766,119892,119960,120088,120232,120360,120429,120524,120639,120752,120851,120960,121071,121182,121283,121388,121488,121618,121709,121832,121926,122038,122124,122228,122324,122412,122530,122634,122738,122864,122952,123060,123160,123250,123360,123444,123546,123630,123684,123748,123854,123940,124050,124134,124254"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/a0dbffbed5b16c06879115d637ff7d23/transformed/jetified-activity-1.6.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "274", "startColumns": "4", "startOffsets": "17409", "endColumns": "59", "endOffsets": "17464"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/2fee84ce99728401b49e699b68198397/transformed/fragment-1.3.6/res/values/values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "248,254,277", "startColumns": "4,4,4", "startOffsets": "16107,16364,17573", "endColumns": "56,64,63", "endOffsets": "16159,16424,17632"}}]}, {"outputFile": "/Users/<USER>/.gradle/daemon/7.3.3/com.wkb.custominputbox3-mergeReleaseResources-18:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/7002a03739265e0a14b05d2e207153d0/transformed/jetified-savedstate-1.2.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "275", "startColumns": "4", "startOffsets": "17469", "endColumns": "53", "endOffsets": "17518"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "311,319,320,321,322,323,324,325,326,327,328,329,330,331,332,334,335,336,341,342,343,344,345,346,347,348,349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19899,20462,20498,20543,20577,20613,20705,20775,20810,20845,20881,20916,20950,20984,21039,21153,21189,21223,21426,21478,21552,21602,21671,21725,21775,21811,21845", "endLines": "311,319,320,321,322,323,324,325,326,327,328,329,330,331,332,334,335,339,341,342,343,344,345,346,347,348,349", "endColumns": "51,35,44,33,35,91,69,34,34,35,34,33,33,54,60,35,33,13,51,73,49,68,53,49,35,33,34", "endOffsets": "19946,20493,20538,20572,20608,20700,20770,20805,20840,20876,20911,20945,20979,21034,21095,21184,21218,21350,21473,21547,21597,21666,21720,21770,21806,21840,21875"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/91e8016f0a249a956def1658dacf6ac4/transformed/lifecycle-runtime-2.5.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "273", "startColumns": "4", "startOffsets": "17366", "endColumns": "42", "endOffsets": "17404"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/attrs.xml", "from": {"startLines": "-1,-1,-1,3", "startColumns": "-1,-1,-1,4", "startOffsets": "-1,-1,-1,56", "endLines": "-1,-1,-1,33", "endColumns": "-1,-1,-1,24", "endOffsets": "-1,-1,-1,1714"}, "to": {"startLines": "5,8,11,2575", "startColumns": "4,4,4,4", "startOffsets": "299,405,510,145769", "endLines": "7,10,17,2605", "endColumns": "11,11,11,24", "endOffsets": "400,505,761,147398"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/30c6f81231359517120429df1c780a80/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "310", "startColumns": "4", "startOffsets": "19816", "endColumns": "82", "endOffsets": "19894"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6740173314278d5dc47a04980597f4e3/transformed/lifecycle-viewmodel-2.5.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "276", "startColumns": "4", "startOffsets": "17523", "endColumns": "49", "endOffsets": "17568"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "18,29,30,43,44,67,68,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,250,251,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,282,312,313,314,315,316,317,318,340,1709,1710,1714,1715,1719,1863,1864", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,1485,1557,2605,2670,4089,4158,11076,11146,11214,11286,11356,11417,11491,12348,12409,12470,12532,12596,12658,12719,12787,12887,12947,13013,13086,13155,13212,13264,13779,13851,13927,13992,14051,14110,14170,14230,14290,14350,14410,14470,14530,14590,14650,14710,14769,14829,14889,14949,15009,15069,15129,15189,15249,15309,15369,15428,15488,15548,15607,15666,15725,15784,15843,16198,16233,16475,16530,16593,16648,16706,16764,16825,16888,16945,16996,17046,17107,17164,17230,17264,17299,17880,19951,20018,20090,20159,20228,20302,20374,21355,112206,112323,112524,112634,112835,124259,124331", "endLines": "18,29,30,43,44,67,68,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,250,251,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,282,312,313,314,315,316,317,318,340,1709,1713,1714,1718,1719,1863,1864", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "821,1552,1640,2665,2731,4153,4216,11141,11209,11281,11351,11412,11486,11559,12404,12465,12527,12591,12653,12714,12782,12882,12942,13008,13081,13150,13207,13259,13321,13846,13922,13987,14046,14105,14165,14225,14285,14345,14405,14465,14525,14585,14645,14705,14764,14824,14884,14944,15004,15064,15124,15184,15244,15304,15364,15423,15483,15543,15602,15661,15720,15779,15838,15897,16228,16263,16525,16588,16643,16701,16759,16820,16883,16940,16991,17041,17102,17159,17225,17259,17294,17329,17945,20013,20085,20154,20223,20297,20369,20457,21421,112318,112519,112629,112830,112959,124326,124393"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1865,1868,1872", "startColumns": "4,4,4", "startOffsets": "124398,124515,124667", "endLines": "1867,1871,1883", "endColumns": "12,12,12", "endOffsets": "124510,124662,125294"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/10721da65f20075e1e7ee43161f80a4f/transformed/appcompat-1.6.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,19,20,21,22,23,24,25,26,27,28,31,32,33,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,201,202,203,204,205,206,207,208,244,245,246,247,249,252,253,255,272,278,279,280,281,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,333,350,351,352,353,354,355,363,364,368,372,376,381,387,394,398,402,407,411,415,419,423,427,431,437,441,447,451,457,461,466,470,473,477,483,487,493,497,503,506,510,514,518,522,526,527,528,529,532,535,538,541,545,546,547,548,549,552,554,556,558,563,564,568,574,578,579,581,593,594,598,604,608,609,610,614,641,645,646,650,678,850,876,1047,1073,1104,1112,1118,1134,1156,1161,1166,1176,1185,1194,1198,1205,1224,1231,1232,1241,1244,1247,1251,1255,1259,1262,1263,1268,1273,1283,1288,1295,1301,1302,1305,1309,1314,1316,1318,1321,1324,1326,1330,1333,1340,1343,1346,1350,1352,1356,1358,1360,1362,1366,1374,1382,1394,1400,1409,1412,1423,1426,1427,1432,1433,1438,1507,1577,1578,1588,1597,1598,1600,1604,1607,1610,1613,1616,1619,1622,1625,1629,1632,1635,1638,1642,1645,1649,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1686,1687,1689,1690,1692,1694,1695,1697,1698,1699,1700,1701,1702,1704,1705,1706,1707,1708,1720,1722,1724,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1740,1741,1742,1743,1744,1745,1746,1748,1752,1756,1757,1758,1759,1760,1761,1765,1766,1767,1768,1770,1772,1774,1776,1778,1779,1780,1781,1783,1785,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1801,1802,1803,1804,1806,1808,1809,1811,1812,1814,1816,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1831,1832,1833,1834,1836,1837,1838,1839,1840,1842,1844,1846,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,826,867,922,984,1048,1118,1179,1254,1330,1407,1645,1730,1812,1888,1964,2041,2119,2225,2331,2410,2490,2547,2736,2810,2885,2950,3016,3076,3137,3209,3282,3349,3417,3476,3535,3594,3653,3712,3766,3820,3873,3927,3981,4035,4221,4295,4374,4447,4521,4592,4664,4736,4809,4866,4924,4997,5071,5145,5220,5292,5365,5435,5506,5566,5627,5696,5765,5835,5909,5985,6049,6126,6202,6279,6344,6413,6490,6565,6634,6702,6779,6845,6906,7003,7068,7137,7236,7307,7366,7424,7481,7540,7604,7675,7747,7819,7891,7963,8030,8098,8166,8225,8288,8352,8442,8533,8593,8659,8726,8792,8862,8926,8979,9046,9107,9174,9287,9345,9408,9473,9538,9613,9686,9758,9802,9849,9895,9944,10005,10066,10127,10189,10253,10317,10381,10446,10509,10569,10630,10696,10755,10815,10877,10948,11008,11564,11650,11737,11827,11914,12002,12084,12167,12257,13326,13378,13436,13481,13547,13611,13668,13725,15902,15959,16007,16056,16164,16268,16315,16429,17334,17637,17701,17763,17823,17950,18024,18094,18172,18226,18296,18381,18429,18475,18536,18599,18665,18729,18800,18863,18928,18992,19053,19114,19166,19239,19313,19382,19457,19531,19605,19746,21100,21880,21958,22048,22136,22232,22322,22904,22993,23240,23521,23773,24058,24451,24928,25150,25372,25648,25875,26105,26335,26565,26795,27022,27441,27667,28092,28322,28750,28969,29252,29460,29591,29818,30244,30469,30896,31117,31542,31662,31938,32239,32563,32854,33168,33305,33436,33541,33783,33950,34154,34362,34633,34745,34857,34962,35079,35293,35439,35579,35665,36013,36101,36347,36765,37014,37096,37194,37851,37951,38203,38627,38882,38976,39065,39302,41326,41568,41670,41923,44079,54760,56276,66971,68499,70256,70882,71302,72563,73828,74084,74320,74867,75361,75966,76164,76744,78112,78487,78605,79143,79300,79496,79769,80025,80195,80336,80400,80765,81132,81808,82072,82410,82763,82857,83043,83349,83611,83736,83863,84102,84313,84432,84625,84802,85257,85438,85560,85819,85932,86119,86221,86328,86457,86732,87240,87736,88613,88907,89477,89626,90358,90530,90614,90950,91042,91320,96551,101922,101984,102562,103146,103237,103350,103579,103739,103891,104062,104228,104397,104564,104727,104970,105140,105313,105484,105758,105957,106162,106492,106576,106672,106768,106866,106966,107068,107170,107272,107374,107476,107576,107672,107784,107913,108036,108167,108298,108396,108510,108604,108744,108878,108974,109086,109186,109302,109398,109510,109610,109750,109886,110050,110180,110338,110488,110629,110773,110908,111020,111170,111298,111426,111562,111694,111824,111954,112066,112964,113110,113254,113392,113458,113548,113624,113728,113818,113920,114028,114136,114236,114316,114408,114506,114616,114668,114746,114852,114944,115048,115158,115280,115443,115600,115680,115780,115870,115980,116070,116311,116405,116511,116603,116703,116815,116929,117045,117161,117255,117369,117481,117583,117703,117825,117907,118011,118131,118257,118355,118449,118537,118649,118765,118887,118999,119174,119290,119376,119468,119580,119704,119771,119897,119965,120093,120237,120365,120434,120529,120644,120757,120856,120965,121076,121187,121288,121393,121493,121623,121714,121837,121931,122043,122129,122233,122329,122417,122535,122639,122743,122869,122957,123065,123165,123255,123365,123449,123551,123635,123689,123753,123859,123945,124055,124139", "endLines": "2,3,4,19,20,21,22,23,24,25,26,27,28,31,32,33,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,201,202,203,204,205,206,207,208,244,245,246,247,249,252,253,255,272,278,279,280,281,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,333,350,351,352,353,354,362,363,367,371,375,380,386,393,397,401,406,410,414,418,422,426,430,436,440,446,450,456,460,465,469,472,476,482,486,492,496,502,505,509,513,517,521,525,526,527,528,531,534,537,540,544,545,546,547,548,551,553,555,557,562,563,567,573,577,578,580,592,593,597,603,607,608,609,613,640,644,645,649,677,849,875,1046,1072,1103,1111,1117,1133,1155,1160,1165,1175,1184,1193,1197,1204,1223,1230,1231,1240,1243,1246,1250,1254,1258,1261,1262,1267,1272,1282,1287,1294,1300,1301,1304,1308,1313,1315,1317,1320,1323,1325,1329,1332,1339,1342,1345,1349,1351,1355,1357,1359,1361,1365,1373,1381,1393,1399,1408,1411,1422,1425,1426,1431,1432,1437,1506,1576,1577,1587,1596,1597,1599,1603,1606,1609,1612,1615,1618,1621,1624,1628,1631,1634,1637,1641,1644,1648,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1685,1686,1688,1689,1691,1693,1694,1696,1697,1698,1699,1700,1701,1703,1704,1705,1706,1707,1708,1721,1723,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1739,1740,1741,1742,1743,1744,1745,1747,1751,1755,1756,1757,1758,1759,1760,1764,1765,1766,1767,1769,1771,1773,1775,1777,1778,1779,1780,1782,1784,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1800,1801,1802,1803,1805,1807,1808,1810,1811,1813,1815,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1830,1831,1832,1833,1835,1836,1837,1838,1839,1841,1843,1845,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,294,862,917,979,1043,1113,1174,1249,1325,1402,1480,1725,1807,1883,1959,2036,2114,2220,2326,2405,2485,2542,2600,2805,2880,2945,3011,3071,3132,3204,3277,3344,3412,3471,3530,3589,3648,3707,3761,3815,3868,3922,3976,4030,4084,4290,4369,4442,4516,4587,4659,4731,4804,4861,4919,4992,5066,5140,5215,5287,5360,5430,5501,5561,5622,5691,5760,5830,5904,5980,6044,6121,6197,6274,6339,6408,6485,6560,6629,6697,6774,6840,6901,6998,7063,7132,7231,7302,7361,7419,7476,7535,7599,7670,7742,7814,7886,7958,8025,8093,8161,8220,8283,8347,8437,8528,8588,8654,8721,8787,8857,8921,8974,9041,9102,9169,9282,9340,9403,9468,9533,9608,9681,9753,9797,9844,9890,9939,10000,10061,10122,10184,10248,10312,10376,10441,10504,10564,10625,10691,10750,10810,10872,10943,11003,11071,11645,11732,11822,11909,11997,12079,12162,12252,12343,13373,13431,13476,13542,13606,13663,13720,13774,15954,16002,16051,16102,16193,16310,16359,16470,17361,17696,17758,17818,17875,18019,18089,18167,18221,18291,18376,18424,18470,18531,18594,18660,18724,18795,18858,18923,18987,19048,19109,19161,19234,19308,19377,19452,19526,19600,19741,19811,21148,21953,22043,22131,22227,22317,22899,22988,23235,23516,23768,24053,24446,24923,25145,25367,25643,25870,26100,26330,26560,26790,27017,27436,27662,28087,28317,28745,28964,29247,29455,29586,29813,30239,30464,30891,31112,31537,31657,31933,32234,32558,32849,33163,33300,33431,33536,33778,33945,34149,34357,34628,34740,34852,34957,35074,35288,35434,35574,35660,36008,36096,36342,36760,37009,37091,37189,37846,37946,38198,38622,38877,38971,39060,39297,41321,41563,41665,41918,44074,54755,56271,66966,68494,70251,70877,71297,72558,73823,74079,74315,74862,75356,75961,76159,76739,78107,78482,78600,79138,79295,79491,79764,80020,80190,80331,80395,80760,81127,81803,82067,82405,82758,82852,83038,83344,83606,83731,83858,84097,84308,84427,84620,84797,85252,85433,85555,85814,85927,86114,86216,86323,86452,86727,87235,87731,88608,88902,89472,89621,90353,90525,90609,90945,91037,91315,96546,101917,101979,102557,103141,103232,103345,103574,103734,103886,104057,104223,104392,104559,104722,104965,105135,105308,105479,105753,105952,106157,106487,106571,106667,106763,106861,106961,107063,107165,107267,107369,107471,107571,107667,107779,107908,108031,108162,108293,108391,108505,108599,108739,108873,108969,109081,109181,109297,109393,109505,109605,109745,109881,110045,110175,110333,110483,110624,110768,110903,111015,111165,111293,111421,111557,111689,111819,111949,112061,112201,113105,113249,113387,113453,113543,113619,113723,113813,113915,114023,114131,114231,114311,114403,114501,114611,114663,114741,114847,114939,115043,115153,115275,115438,115595,115675,115775,115865,115975,116065,116306,116400,116506,116598,116698,116810,116924,117040,117156,117250,117364,117476,117578,117698,117820,117902,118006,118126,118252,118350,118444,118532,118644,118760,118882,118994,119169,119285,119371,119463,119575,119699,119766,119892,119960,120088,120232,120360,120429,120524,120639,120752,120851,120960,121071,121182,121283,121388,121488,121618,121709,121832,121926,122038,122124,122228,122324,122412,122530,122634,122738,122864,122952,123060,123160,123250,123360,123444,123546,123630,123684,123748,123854,123940,124050,124134,124254"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/a0dbffbed5b16c06879115d637ff7d23/transformed/jetified-activity-1.6.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "274", "startColumns": "4", "startOffsets": "17409", "endColumns": "59", "endOffsets": "17464"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/2fee84ce99728401b49e699b68198397/transformed/fragment-1.3.6/res/values/values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "248,254,277", "startColumns": "4,4,4", "startOffsets": "16107,16364,17573", "endColumns": "56,64,63", "endOffsets": "16159,16424,17632"}}]}, {"outputFile": "com.wkb.custominputbox3-mergeReleaseResources-18:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/7002a03739265e0a14b05d2e207153d0/transformed/jetified-savedstate-1.2.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "275", "startColumns": "4", "startOffsets": "17469", "endColumns": "53", "endOffsets": "17518"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "311,319,320,321,322,323,324,325,326,327,328,329,330,331,332,334,335,336,341,342,343,344,345,346,347,348,349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19899,20462,20498,20543,20577,20613,20705,20775,20810,20845,20881,20916,20950,20984,21039,21153,21189,21223,21426,21478,21552,21602,21671,21725,21775,21811,21845", "endLines": "311,319,320,321,322,323,324,325,326,327,328,329,330,331,332,334,335,339,341,342,343,344,345,346,347,348,349", "endColumns": "51,35,44,33,35,91,69,34,34,35,34,33,33,54,60,35,33,13,51,73,49,68,53,49,35,33,34", "endOffsets": "19946,20493,20538,20572,20608,20700,20770,20805,20840,20876,20911,20945,20979,21034,21095,21184,21218,21350,21473,21547,21597,21666,21720,21770,21806,21840,21875"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/91e8016f0a249a956def1658dacf6ac4/transformed/lifecycle-runtime-2.5.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "273", "startColumns": "4", "startOffsets": "17366", "endColumns": "42", "endOffsets": "17404"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/attrs.xml", "from": {"startLines": "-1,-1,-1,3", "startColumns": "-1,-1,-1,4", "startOffsets": "-1,-1,-1,56", "endLines": "-1,-1,-1,34", "endColumns": "-1,-1,-1,24", "endOffsets": "-1,-1,-1,1773"}, "to": {"startLines": "5,8,11,2575", "startColumns": "4,4,4,4", "startOffsets": "299,405,510,145769", "endLines": "7,10,17,2606", "endColumns": "11,11,11,24", "endOffsets": "400,505,761,147456"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/30c6f81231359517120429df1c780a80/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "310", "startColumns": "4", "startOffsets": "19816", "endColumns": "82", "endOffsets": "19894"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6740173314278d5dc47a04980597f4e3/transformed/lifecycle-viewmodel-2.5.1/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "276", "startColumns": "4", "startOffsets": "17523", "endColumns": "49", "endOffsets": "17568"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "18,29,30,43,44,67,68,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,250,251,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,282,312,313,314,315,316,317,318,340,1709,1710,1714,1715,1719,1863,1864", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,1485,1557,2605,2670,4089,4158,11076,11146,11214,11286,11356,11417,11491,12348,12409,12470,12532,12596,12658,12719,12787,12887,12947,13013,13086,13155,13212,13264,13779,13851,13927,13992,14051,14110,14170,14230,14290,14350,14410,14470,14530,14590,14650,14710,14769,14829,14889,14949,15009,15069,15129,15189,15249,15309,15369,15428,15488,15548,15607,15666,15725,15784,15843,16198,16233,16475,16530,16593,16648,16706,16764,16825,16888,16945,16996,17046,17107,17164,17230,17264,17299,17880,19951,20018,20090,20159,20228,20302,20374,21355,112206,112323,112524,112634,112835,124259,124331", "endLines": "18,29,30,43,44,67,68,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,250,251,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,282,312,313,314,315,316,317,318,340,1709,1713,1714,1718,1719,1863,1864", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "821,1552,1640,2665,2731,4153,4216,11141,11209,11281,11351,11412,11486,11559,12404,12465,12527,12591,12653,12714,12782,12882,12942,13008,13081,13150,13207,13259,13321,13846,13922,13987,14046,14105,14165,14225,14285,14345,14405,14465,14525,14585,14645,14705,14764,14824,14884,14944,15004,15064,15124,15184,15244,15304,15364,15423,15483,15543,15602,15661,15720,15779,15838,15897,16228,16263,16525,16588,16643,16701,16759,16820,16883,16940,16991,17041,17102,17159,17225,17259,17294,17329,17945,20013,20085,20154,20223,20297,20369,20457,21421,112318,112519,112629,112830,112959,124326,124393"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "1865,1868,1872", "startColumns": "4,4,4", "startOffsets": "124398,124515,124667", "endLines": "1867,1871,1883", "endColumns": "12,12,12", "endOffsets": "124510,124662,125294"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/10721da65f20075e1e7ee43161f80a4f/transformed/appcompat-1.6.1/res/values/values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,19,20,21,22,23,24,25,26,27,28,31,32,33,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,201,202,203,204,205,206,207,208,244,245,246,247,249,252,253,255,272,278,279,280,281,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,333,350,351,352,353,354,355,363,364,368,372,376,381,387,394,398,402,407,411,415,419,423,427,431,437,441,447,451,457,461,466,470,473,477,483,487,493,497,503,506,510,514,518,522,526,527,528,529,532,535,538,541,545,546,547,548,549,552,554,556,558,563,564,568,574,578,579,581,593,594,598,604,608,609,610,614,641,645,646,650,678,850,876,1047,1073,1104,1112,1118,1134,1156,1161,1166,1176,1185,1194,1198,1205,1224,1231,1232,1241,1244,1247,1251,1255,1259,1262,1263,1268,1273,1283,1288,1295,1301,1302,1305,1309,1314,1316,1318,1321,1324,1326,1330,1333,1340,1343,1346,1350,1352,1356,1358,1360,1362,1366,1374,1382,1394,1400,1409,1412,1423,1426,1427,1432,1433,1438,1507,1577,1578,1588,1597,1598,1600,1604,1607,1610,1613,1616,1619,1622,1625,1629,1632,1635,1638,1642,1645,1649,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1686,1687,1689,1690,1692,1694,1695,1697,1698,1699,1700,1701,1702,1704,1705,1706,1707,1708,1720,1722,1724,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1740,1741,1742,1743,1744,1745,1746,1748,1752,1756,1757,1758,1759,1760,1761,1765,1766,1767,1768,1770,1772,1774,1776,1778,1779,1780,1781,1783,1785,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1801,1802,1803,1804,1806,1808,1809,1811,1812,1814,1816,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1831,1832,1833,1834,1836,1837,1838,1839,1840,1842,1844,1846,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,826,867,922,984,1048,1118,1179,1254,1330,1407,1645,1730,1812,1888,1964,2041,2119,2225,2331,2410,2490,2547,2736,2810,2885,2950,3016,3076,3137,3209,3282,3349,3417,3476,3535,3594,3653,3712,3766,3820,3873,3927,3981,4035,4221,4295,4374,4447,4521,4592,4664,4736,4809,4866,4924,4997,5071,5145,5220,5292,5365,5435,5506,5566,5627,5696,5765,5835,5909,5985,6049,6126,6202,6279,6344,6413,6490,6565,6634,6702,6779,6845,6906,7003,7068,7137,7236,7307,7366,7424,7481,7540,7604,7675,7747,7819,7891,7963,8030,8098,8166,8225,8288,8352,8442,8533,8593,8659,8726,8792,8862,8926,8979,9046,9107,9174,9287,9345,9408,9473,9538,9613,9686,9758,9802,9849,9895,9944,10005,10066,10127,10189,10253,10317,10381,10446,10509,10569,10630,10696,10755,10815,10877,10948,11008,11564,11650,11737,11827,11914,12002,12084,12167,12257,13326,13378,13436,13481,13547,13611,13668,13725,15902,15959,16007,16056,16164,16268,16315,16429,17334,17637,17701,17763,17823,17950,18024,18094,18172,18226,18296,18381,18429,18475,18536,18599,18665,18729,18800,18863,18928,18992,19053,19114,19166,19239,19313,19382,19457,19531,19605,19746,21100,21880,21958,22048,22136,22232,22322,22904,22993,23240,23521,23773,24058,24451,24928,25150,25372,25648,25875,26105,26335,26565,26795,27022,27441,27667,28092,28322,28750,28969,29252,29460,29591,29818,30244,30469,30896,31117,31542,31662,31938,32239,32563,32854,33168,33305,33436,33541,33783,33950,34154,34362,34633,34745,34857,34962,35079,35293,35439,35579,35665,36013,36101,36347,36765,37014,37096,37194,37851,37951,38203,38627,38882,38976,39065,39302,41326,41568,41670,41923,44079,54760,56276,66971,68499,70256,70882,71302,72563,73828,74084,74320,74867,75361,75966,76164,76744,78112,78487,78605,79143,79300,79496,79769,80025,80195,80336,80400,80765,81132,81808,82072,82410,82763,82857,83043,83349,83611,83736,83863,84102,84313,84432,84625,84802,85257,85438,85560,85819,85932,86119,86221,86328,86457,86732,87240,87736,88613,88907,89477,89626,90358,90530,90614,90950,91042,91320,96551,101922,101984,102562,103146,103237,103350,103579,103739,103891,104062,104228,104397,104564,104727,104970,105140,105313,105484,105758,105957,106162,106492,106576,106672,106768,106866,106966,107068,107170,107272,107374,107476,107576,107672,107784,107913,108036,108167,108298,108396,108510,108604,108744,108878,108974,109086,109186,109302,109398,109510,109610,109750,109886,110050,110180,110338,110488,110629,110773,110908,111020,111170,111298,111426,111562,111694,111824,111954,112066,112964,113110,113254,113392,113458,113548,113624,113728,113818,113920,114028,114136,114236,114316,114408,114506,114616,114668,114746,114852,114944,115048,115158,115280,115443,115600,115680,115780,115870,115980,116070,116311,116405,116511,116603,116703,116815,116929,117045,117161,117255,117369,117481,117583,117703,117825,117907,118011,118131,118257,118355,118449,118537,118649,118765,118887,118999,119174,119290,119376,119468,119580,119704,119771,119897,119965,120093,120237,120365,120434,120529,120644,120757,120856,120965,121076,121187,121288,121393,121493,121623,121714,121837,121931,122043,122129,122233,122329,122417,122535,122639,122743,122869,122957,123065,123165,123255,123365,123449,123551,123635,123689,123753,123859,123945,124055,124139", "endLines": "2,3,4,19,20,21,22,23,24,25,26,27,28,31,32,33,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,201,202,203,204,205,206,207,208,244,245,246,247,249,252,253,255,272,278,279,280,281,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,333,350,351,352,353,354,362,363,367,371,375,380,386,393,397,401,406,410,414,418,422,426,430,436,440,446,450,456,460,465,469,472,476,482,486,492,496,502,505,509,513,517,521,525,526,527,528,531,534,537,540,544,545,546,547,548,551,553,555,557,562,563,567,573,577,578,580,592,593,597,603,607,608,609,613,640,644,645,649,677,849,875,1046,1072,1103,1111,1117,1133,1155,1160,1165,1175,1184,1193,1197,1204,1223,1230,1231,1240,1243,1246,1250,1254,1258,1261,1262,1267,1272,1282,1287,1294,1300,1301,1304,1308,1313,1315,1317,1320,1323,1325,1329,1332,1339,1342,1345,1349,1351,1355,1357,1359,1361,1365,1373,1381,1393,1399,1408,1411,1422,1425,1426,1431,1432,1437,1506,1576,1577,1587,1596,1597,1599,1603,1606,1609,1612,1615,1618,1621,1624,1628,1631,1634,1637,1641,1644,1648,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1685,1686,1688,1689,1691,1693,1694,1696,1697,1698,1699,1700,1701,1703,1704,1705,1706,1707,1708,1721,1723,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1739,1740,1741,1742,1743,1744,1745,1747,1751,1755,1756,1757,1758,1759,1760,1764,1765,1766,1767,1769,1771,1773,1775,1777,1778,1779,1780,1782,1784,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1800,1801,1802,1803,1805,1807,1808,1810,1811,1813,1815,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1830,1831,1832,1833,1835,1836,1837,1838,1839,1841,1843,1845,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,294,862,917,979,1043,1113,1174,1249,1325,1402,1480,1725,1807,1883,1959,2036,2114,2220,2326,2405,2485,2542,2600,2805,2880,2945,3011,3071,3132,3204,3277,3344,3412,3471,3530,3589,3648,3707,3761,3815,3868,3922,3976,4030,4084,4290,4369,4442,4516,4587,4659,4731,4804,4861,4919,4992,5066,5140,5215,5287,5360,5430,5501,5561,5622,5691,5760,5830,5904,5980,6044,6121,6197,6274,6339,6408,6485,6560,6629,6697,6774,6840,6901,6998,7063,7132,7231,7302,7361,7419,7476,7535,7599,7670,7742,7814,7886,7958,8025,8093,8161,8220,8283,8347,8437,8528,8588,8654,8721,8787,8857,8921,8974,9041,9102,9169,9282,9340,9403,9468,9533,9608,9681,9753,9797,9844,9890,9939,10000,10061,10122,10184,10248,10312,10376,10441,10504,10564,10625,10691,10750,10810,10872,10943,11003,11071,11645,11732,11822,11909,11997,12079,12162,12252,12343,13373,13431,13476,13542,13606,13663,13720,13774,15954,16002,16051,16102,16193,16310,16359,16470,17361,17696,17758,17818,17875,18019,18089,18167,18221,18291,18376,18424,18470,18531,18594,18660,18724,18795,18858,18923,18987,19048,19109,19161,19234,19308,19377,19452,19526,19600,19741,19811,21148,21953,22043,22131,22227,22317,22899,22988,23235,23516,23768,24053,24446,24923,25145,25367,25643,25870,26100,26330,26560,26790,27017,27436,27662,28087,28317,28745,28964,29247,29455,29586,29813,30239,30464,30891,31112,31537,31657,31933,32234,32558,32849,33163,33300,33431,33536,33778,33945,34149,34357,34628,34740,34852,34957,35074,35288,35434,35574,35660,36008,36096,36342,36760,37009,37091,37189,37846,37946,38198,38622,38877,38971,39060,39297,41321,41563,41665,41918,44074,54755,56271,66966,68494,70251,70877,71297,72558,73823,74079,74315,74862,75356,75961,76159,76739,78107,78482,78600,79138,79295,79491,79764,80020,80190,80331,80395,80760,81127,81803,82067,82405,82758,82852,83038,83344,83606,83731,83858,84097,84308,84427,84620,84797,85252,85433,85555,85814,85927,86114,86216,86323,86452,86727,87235,87731,88608,88902,89472,89621,90353,90525,90609,90945,91037,91315,96546,101917,101979,102557,103141,103232,103345,103574,103734,103886,104057,104223,104392,104559,104722,104965,105135,105308,105479,105753,105952,106157,106487,106571,106667,106763,106861,106961,107063,107165,107267,107369,107471,107571,107667,107779,107908,108031,108162,108293,108391,108505,108599,108739,108873,108969,109081,109181,109297,109393,109505,109605,109745,109881,110045,110175,110333,110483,110624,110768,110903,111015,111165,111293,111421,111557,111689,111819,111949,112061,112201,113105,113249,113387,113453,113543,113619,113723,113813,113915,114023,114131,114231,114311,114403,114501,114611,114663,114741,114847,114939,115043,115153,115275,115438,115595,115675,115775,115865,115975,116065,116306,116400,116506,116598,116698,116810,116924,117040,117156,117250,117364,117476,117578,117698,117820,117902,118006,118126,118252,118350,118444,118532,118644,118760,118882,118994,119169,119285,119371,119463,119575,119699,119766,119892,119960,120088,120232,120360,120429,120524,120639,120752,120851,120960,121071,121182,121283,121388,121488,121618,121709,121832,121926,122038,122124,122228,122324,122412,122530,122634,122738,122864,122952,123060,123160,123250,123360,123444,123546,123630,123684,123748,123854,123940,124050,124134,124254"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/a0dbffbed5b16c06879115d637ff7d23/transformed/jetified-activity-1.6.0/res/values/values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "274", "startColumns": "4", "startOffsets": "17409", "endColumns": "59", "endOffsets": "17464"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/2fee84ce99728401b49e699b68198397/transformed/fragment-1.3.6/res/values/values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "248,254,277", "startColumns": "4,4,4", "startOffsets": "16107,16364,17573", "endColumns": "56,64,63", "endOffsets": "16159,16424,17632"}}]}, {"outputFile": "/Users/<USER>/.gradle/daemon/7.3.3/com.wkb.custominputbox3-merged_res-20:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/transforms-3/7002a03739265e0a14b05d2e207153d0/transformed/jetified-savedstate-1.2.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "275", "startColumns": "4", "startOffsets": "17469", "endColumns": "53", "endOffsets": "17518"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml", "from": {"startLines": "1,28,4,29,25,9,10,22,21,27,26,30,18,2,3,24,23,5,16,13,15,12,11,14,20,19,17", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "16,1258,184,1294,1151,361,453,1046,1011,1222,1187,1328,907,68,123,1115,1081,229,820,646,770,577,523,720,975,941,872", "endLines": "1,28,4,29,25,9,10,22,21,27,26,30,18,2,3,24,23,8,16,13,15,12,11,14,20,19,17", "endColumns": "51,35,44,33,35,91,69,34,34,35,34,33,33,54,60,35,33,13,51,73,49,68,53,49,35,33,34", "endOffsets": "63,1289,224,1323,1182,448,518,1076,1041,1253,1217,1357,936,118,179,1146,1110,356,867,715,815,641,572,765,1006,970,902"}, "to": {"startLines": "311,319,320,321,322,323,324,325,326,327,328,329,330,331,332,334,335,336,341,342,343,344,345,346,347,348,349", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19899,20462,20498,20543,20577,20613,20705,20775,20810,20845,20881,20916,20950,20984,21039,21153,21189,21223,21426,21478,21552,21602,21671,21725,21775,21811,21845", "endLines": "311,319,320,321,322,323,324,325,326,327,328,329,330,331,332,334,335,339,341,342,343,344,345,346,347,348,349", "endColumns": "51,35,44,33,35,91,69,34,34,35,34,33,33,54,60,35,33,13,51,73,49,68,53,49,35,33,34", "endOffsets": "19946,20493,20538,20572,20608,20700,20770,20805,20840,20876,20911,20945,20979,21034,21095,21184,21218,21350,21473,21547,21597,21666,21720,21770,21806,21840,21875"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/91e8016f0a249a956def1658dacf6ac4/transformed/lifecycle-runtime-2.5.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "273", "startColumns": "4", "startOffsets": "17366", "endColumns": "42", "endOffsets": "17404"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/attrs.xml", "from": {"startLines": "43,39,31,3,47", "startColumns": "4,4,4,4,4", "startOffsets": "1843,1737,1480,56,1950", "endLines": "45,41,37,29,60", "endColumns": "11,11,11,24,24", "endOffsets": "1944,1837,1731,1474,2643"}, "to": {"startLines": "5,8,11,2601,2949", "startColumns": "4,4,4,4,4", "startOffsets": "299,405,510,149659,161685", "endLines": "7,10,17,2627,2962", "endColumns": "11,11,11,24,24", "endOffsets": "400,505,761,151052,162378"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/30c6f81231359517120429df1c780a80/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "310", "startColumns": "4", "startOffsets": "19816", "endColumns": "82", "endOffsets": "19894"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/13ba891abf0c9f0c1e731a35fa2f0cca/transformed/jetified-appcompat-resources-1.6.1/res/values/values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "1998,2014,2020,3026,3042", "startColumns": "4,4,4,4,4", "startOffsets": "129339,129764,129942,164373,164784", "endLines": "2013,2019,2029,3041,3045", "endColumns": "24,24,24,24,24", "endOffsets": "129759,129937,130221,164779,164906"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/6740173314278d5dc47a04980597f4e3/transformed/lifecycle-viewmodel-2.5.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "276", "startColumns": "4", "startOffsets": "17523", "endColumns": "49", "endOffsets": "17568"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/5b0505b29e3f7c2a26fdd91800748cdb/transformed/core-1.10.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "18,29,30,43,44,67,68,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,250,251,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,282,312,313,314,315,316,317,318,340,1709,1710,1714,1715,1719,1863,1864,2529,2563,2646,2679,2709,2742", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "766,1485,1557,2605,2670,4089,4158,11076,11146,11214,11286,11356,11417,11491,12348,12409,12470,12532,12596,12658,12719,12787,12887,12947,13013,13086,13155,13212,13264,13779,13851,13927,13992,14051,14110,14170,14230,14290,14350,14410,14470,14530,14590,14650,14710,14769,14829,14889,14949,15009,15069,15129,15189,15249,15309,15369,15428,15488,15548,15607,15666,15725,15784,15843,16198,16233,16475,16530,16593,16648,16706,16764,16825,16888,16945,16996,17046,17107,17164,17230,17264,17299,17880,19951,20018,20090,20159,20228,20302,20374,21355,112206,112323,112524,112634,112835,124259,124331,146428,148001,151629,153360,154360,155042", "endLines": "18,29,30,43,44,67,68,170,171,172,173,174,175,176,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,250,251,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,282,312,313,314,315,316,317,318,340,1709,1713,1714,1718,1719,1863,1864,2534,2572,2678,2699,2741,2747", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "821,1552,1640,2665,2731,4153,4216,11141,11209,11281,11351,11412,11486,11559,12404,12465,12527,12591,12653,12714,12782,12882,12942,13008,13081,13150,13207,13259,13321,13846,13922,13987,14046,14105,14165,14225,14285,14345,14405,14465,14525,14585,14645,14705,14764,14824,14884,14944,15004,15064,15124,15184,15244,15304,15364,15423,15483,15543,15602,15661,15720,15779,15838,15897,16228,16263,16525,16588,16643,16701,16759,16820,16883,16940,16991,17041,17102,17159,17225,17259,17294,17329,17945,20013,20085,20154,20223,20297,20369,20457,21421,112318,112519,112629,112830,112959,124326,124393,146626,148297,153355,154036,155037,155204"}}, {"source": "/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml", "from": {"startLines": "15,19,3", "startColumns": "4,4,4", "startOffsets": "737,855,103", "endLines": "17,22,14", "endColumns": "12,12,12", "endOffsets": "849,1002,732"}, "to": {"startLines": "1865,1868,1872", "startColumns": "4,4,4", "startOffsets": "124398,124515,124667", "endLines": "1867,1871,1883", "endColumns": "12,12,12", "endOffsets": "124510,124662,125294"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/10721da65f20075e1e7ee43161f80a4f/transformed/appcompat-1.6.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "2,3,4,19,20,21,22,23,24,25,26,27,28,31,32,33,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,201,202,203,204,205,206,207,208,244,245,246,247,249,252,253,255,272,278,279,280,281,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,333,350,351,352,353,354,355,363,364,368,372,376,381,387,394,398,402,407,411,415,419,423,427,431,437,441,447,451,457,461,466,470,473,477,483,487,493,497,503,506,510,514,518,522,526,527,528,529,532,535,538,541,545,546,547,548,549,552,554,556,558,563,564,568,574,578,579,581,593,594,598,604,608,609,610,614,641,645,646,650,678,850,876,1047,1073,1104,1112,1118,1134,1156,1161,1166,1176,1185,1194,1198,1205,1224,1231,1232,1241,1244,1247,1251,1255,1259,1262,1263,1268,1273,1283,1288,1295,1301,1302,1305,1309,1314,1316,1318,1321,1324,1326,1330,1333,1340,1343,1346,1350,1352,1356,1358,1360,1362,1366,1374,1382,1394,1400,1409,1412,1423,1426,1427,1432,1433,1438,1507,1577,1578,1588,1597,1598,1600,1604,1607,1610,1613,1616,1619,1622,1625,1629,1632,1635,1638,1642,1645,1649,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1686,1687,1689,1690,1692,1694,1695,1697,1698,1699,1700,1701,1702,1704,1705,1706,1707,1708,1720,1722,1724,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1738,1740,1741,1742,1743,1744,1745,1746,1748,1752,1756,1757,1758,1759,1760,1761,1765,1766,1767,1768,1770,1772,1774,1776,1778,1779,1780,1781,1783,1785,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1801,1802,1803,1804,1806,1808,1809,1811,1812,1814,1816,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1829,1831,1832,1833,1834,1836,1837,1838,1839,1840,1842,1844,1846,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1884,1959,1962,1965,1968,1982,1988,2030,2033,2062,2089,2098,2162,2525,2535,2573,2628,2748,2772,2778,2784,2805,2929,2963,2969,2973,2979,3014,3046,3112,3132,3187,3199,3225", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,826,867,922,984,1048,1118,1179,1254,1330,1407,1645,1730,1812,1888,1964,2041,2119,2225,2331,2410,2490,2547,2736,2810,2885,2950,3016,3076,3137,3209,3282,3349,3417,3476,3535,3594,3653,3712,3766,3820,3873,3927,3981,4035,4221,4295,4374,4447,4521,4592,4664,4736,4809,4866,4924,4997,5071,5145,5220,5292,5365,5435,5506,5566,5627,5696,5765,5835,5909,5985,6049,6126,6202,6279,6344,6413,6490,6565,6634,6702,6779,6845,6906,7003,7068,7137,7236,7307,7366,7424,7481,7540,7604,7675,7747,7819,7891,7963,8030,8098,8166,8225,8288,8352,8442,8533,8593,8659,8726,8792,8862,8926,8979,9046,9107,9174,9287,9345,9408,9473,9538,9613,9686,9758,9802,9849,9895,9944,10005,10066,10127,10189,10253,10317,10381,10446,10509,10569,10630,10696,10755,10815,10877,10948,11008,11564,11650,11737,11827,11914,12002,12084,12167,12257,13326,13378,13436,13481,13547,13611,13668,13725,15902,15959,16007,16056,16164,16268,16315,16429,17334,17637,17701,17763,17823,17950,18024,18094,18172,18226,18296,18381,18429,18475,18536,18599,18665,18729,18800,18863,18928,18992,19053,19114,19166,19239,19313,19382,19457,19531,19605,19746,21100,21880,21958,22048,22136,22232,22322,22904,22993,23240,23521,23773,24058,24451,24928,25150,25372,25648,25875,26105,26335,26565,26795,27022,27441,27667,28092,28322,28750,28969,29252,29460,29591,29818,30244,30469,30896,31117,31542,31662,31938,32239,32563,32854,33168,33305,33436,33541,33783,33950,34154,34362,34633,34745,34857,34962,35079,35293,35439,35579,35665,36013,36101,36347,36765,37014,37096,37194,37851,37951,38203,38627,38882,38976,39065,39302,41326,41568,41670,41923,44079,54760,56276,66971,68499,70256,70882,71302,72563,73828,74084,74320,74867,75361,75966,76164,76744,78112,78487,78605,79143,79300,79496,79769,80025,80195,80336,80400,80765,81132,81808,82072,82410,82763,82857,83043,83349,83611,83736,83863,84102,84313,84432,84625,84802,85257,85438,85560,85819,85932,86119,86221,86328,86457,86732,87240,87736,88613,88907,89477,89626,90358,90530,90614,90950,91042,91320,96551,101922,101984,102562,103146,103237,103350,103579,103739,103891,104062,104228,104397,104564,104727,104970,105140,105313,105484,105758,105957,106162,106492,106576,106672,106768,106866,106966,107068,107170,107272,107374,107476,107576,107672,107784,107913,108036,108167,108298,108396,108510,108604,108744,108878,108974,109086,109186,109302,109398,109510,109610,109750,109886,110050,110180,110338,110488,110629,110773,110908,111020,111170,111298,111426,111562,111694,111824,111954,112066,112964,113110,113254,113392,113458,113548,113624,113728,113818,113920,114028,114136,114236,114316,114408,114506,114616,114668,114746,114852,114944,115048,115158,115280,115443,115600,115680,115780,115870,115980,116070,116311,116405,116511,116603,116703,116815,116929,117045,117161,117255,117369,117481,117583,117703,117825,117907,118011,118131,118257,118355,118449,118537,118649,118765,118887,118999,119174,119290,119376,119468,119580,119704,119771,119897,119965,120093,120237,120365,120434,120529,120644,120757,120856,120965,121076,121187,121288,121393,121493,121623,121714,121837,121931,122043,122129,122233,122329,122417,122535,122639,122743,122869,122957,123065,123165,123255,123365,123449,123551,123635,123689,123753,123859,123945,124055,124139,125299,127915,128033,128148,128228,128589,128822,130226,130304,131648,133009,133397,136240,146293,146631,148302,151057,155209,155960,156222,156422,156801,161079,162383,162612,162763,162978,164061,164911,167937,168681,170812,171152,172463", "endLines": "2,3,4,19,20,21,22,23,24,25,26,27,28,31,32,33,34,35,36,37,38,39,40,41,42,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,177,178,179,180,181,182,183,184,185,201,202,203,204,205,206,207,208,244,245,246,247,249,252,253,255,272,278,279,280,281,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,333,350,351,352,353,354,362,363,367,371,375,380,386,393,397,401,406,410,414,418,422,426,430,436,440,446,450,456,460,465,469,472,476,482,486,492,496,502,505,509,513,517,521,525,526,527,528,531,534,537,540,544,545,546,547,548,551,553,555,557,562,563,567,573,577,578,580,592,593,597,603,607,608,609,613,640,644,645,649,677,849,875,1046,1072,1103,1111,1117,1133,1155,1160,1165,1175,1184,1193,1197,1204,1223,1230,1231,1240,1243,1246,1250,1254,1258,1261,1262,1267,1272,1282,1287,1294,1300,1301,1304,1308,1313,1315,1317,1320,1323,1325,1329,1332,1339,1342,1345,1349,1351,1355,1357,1359,1361,1365,1373,1381,1393,1399,1408,1411,1422,1425,1426,1431,1432,1437,1506,1576,1577,1587,1596,1597,1599,1603,1606,1609,1612,1615,1618,1621,1624,1628,1631,1634,1637,1641,1644,1648,1652,1653,1654,1655,1656,1657,1658,1659,1660,1661,1662,1663,1664,1665,1666,1667,1668,1669,1670,1671,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1685,1686,1688,1689,1691,1693,1694,1696,1697,1698,1699,1700,1701,1703,1704,1705,1706,1707,1708,1721,1723,1725,1726,1727,1728,1729,1730,1731,1732,1733,1734,1735,1736,1737,1739,1740,1741,1742,1743,1744,1745,1747,1751,1755,1756,1757,1758,1759,1760,1764,1765,1766,1767,1769,1771,1773,1775,1777,1778,1779,1780,1782,1784,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1800,1801,1802,1803,1805,1807,1808,1810,1811,1813,1815,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1830,1831,1832,1833,1835,1836,1837,1838,1839,1841,1843,1845,1847,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1861,1862,1958,1961,1964,1967,1981,1987,1997,2032,2061,2088,2097,2161,2524,2528,2562,2600,2645,2771,2777,2783,2804,2928,2948,2968,2972,2978,3013,3025,3111,3131,3186,3198,3224,3231", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,862,917,979,1043,1113,1174,1249,1325,1402,1480,1725,1807,1883,1959,2036,2114,2220,2326,2405,2485,2542,2600,2805,2880,2945,3011,3071,3132,3204,3277,3344,3412,3471,3530,3589,3648,3707,3761,3815,3868,3922,3976,4030,4084,4290,4369,4442,4516,4587,4659,4731,4804,4861,4919,4992,5066,5140,5215,5287,5360,5430,5501,5561,5622,5691,5760,5830,5904,5980,6044,6121,6197,6274,6339,6408,6485,6560,6629,6697,6774,6840,6901,6998,7063,7132,7231,7302,7361,7419,7476,7535,7599,7670,7742,7814,7886,7958,8025,8093,8161,8220,8283,8347,8437,8528,8588,8654,8721,8787,8857,8921,8974,9041,9102,9169,9282,9340,9403,9468,9533,9608,9681,9753,9797,9844,9890,9939,10000,10061,10122,10184,10248,10312,10376,10441,10504,10564,10625,10691,10750,10810,10872,10943,11003,11071,11645,11732,11822,11909,11997,12079,12162,12252,12343,13373,13431,13476,13542,13606,13663,13720,13774,15954,16002,16051,16102,16193,16310,16359,16470,17361,17696,17758,17818,17875,18019,18089,18167,18221,18291,18376,18424,18470,18531,18594,18660,18724,18795,18858,18923,18987,19048,19109,19161,19234,19308,19377,19452,19526,19600,19741,19811,21148,21953,22043,22131,22227,22317,22899,22988,23235,23516,23768,24053,24446,24923,25145,25367,25643,25870,26100,26330,26560,26790,27017,27436,27662,28087,28317,28745,28964,29247,29455,29586,29813,30239,30464,30891,31112,31537,31657,31933,32234,32558,32849,33163,33300,33431,33536,33778,33945,34149,34357,34628,34740,34852,34957,35074,35288,35434,35574,35660,36008,36096,36342,36760,37009,37091,37189,37846,37946,38198,38622,38877,38971,39060,39297,41321,41563,41665,41918,44074,54755,56271,66966,68494,70251,70877,71297,72558,73823,74079,74315,74862,75356,75961,76159,76739,78107,78482,78600,79138,79295,79491,79764,80020,80190,80331,80395,80760,81127,81803,82067,82405,82758,82852,83038,83344,83606,83731,83858,84097,84308,84427,84620,84797,85252,85433,85555,85814,85927,86114,86216,86323,86452,86727,87235,87731,88608,88902,89472,89621,90353,90525,90609,90945,91037,91315,96546,101917,101979,102557,103141,103232,103345,103574,103734,103886,104057,104223,104392,104559,104722,104965,105135,105308,105479,105753,105952,106157,106487,106571,106667,106763,106861,106961,107063,107165,107267,107369,107471,107571,107667,107779,107908,108031,108162,108293,108391,108505,108599,108739,108873,108969,109081,109181,109297,109393,109505,109605,109745,109881,110045,110175,110333,110483,110624,110768,110903,111015,111165,111293,111421,111557,111689,111819,111949,112061,112201,113105,113249,113387,113453,113543,113619,113723,113813,113915,114023,114131,114231,114311,114403,114501,114611,114663,114741,114847,114939,115043,115153,115275,115438,115595,115675,115775,115865,115975,116065,116306,116400,116506,116598,116698,116810,116924,117040,117156,117250,117364,117476,117578,117698,117820,117902,118006,118126,118252,118350,118444,118532,118644,118760,118882,118994,119169,119285,119371,119463,119575,119699,119766,119892,119960,120088,120232,120360,120429,120524,120639,120752,120851,120960,121071,121182,121283,121388,121488,121618,121709,121832,121926,122038,122124,122228,122324,122412,122530,122634,122738,122864,122952,123060,123160,123250,123360,123444,123546,123630,123684,123748,123854,123940,124050,124134,124254,127910,128028,128143,128223,128584,128817,129334,130299,131643,133004,133392,136235,146288,146423,147996,149654,151624,155955,156217,156417,156796,161074,161680,162607,162758,162973,164056,164368,167932,168676,170807,171147,172458,172661"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/a0dbffbed5b16c06879115d637ff7d23/transformed/jetified-activity-1.6.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "59", "endOffsets": "110"}, "to": {"startLines": "274", "startColumns": "4", "startOffsets": "17409", "endColumns": "59", "endOffsets": "17464"}}, {"source": "/Users/<USER>/.gradle/caches/transforms-3/2fee84ce99728401b49e699b68198397/transformed/fragment-1.3.6/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "248,254,277,2700,2705", "startColumns": "4,4,4,4,4", "startOffsets": "16107,16364,17573,154041,154211", "endLines": "248,254,277,2704,2708", "endColumns": "56,64,63,24,24", "endOffsets": "16159,16424,17632,154206,154355"}}]}]}