<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.wkb.custominputbox3.test" >

    <uses-sdk
        android:minSdkVersion="16"
        android:targetSdkVersion="33" />

    <instrumentation
        android:name="androidx.test.runner.AndroidJUnitRunner"
        android:functionalTest="false"
        android:handleProfiling="false"
        android:label="Tests for com.wkb.custominputbox3.test"
        android:targetPackage="com.wkb.custominputbox3.test" />

    <uses-permission android:name="android.permission.REORDER_TASKS" />

    <queries>
        <package android:name="androidx.test.orchestrator" />
        <package android:name="androidx.test.services" />
        <package android:name="com.google.android.apps.common.testing.services" />
    </queries>

    <permission
        android:name="com.wkb.custominputbox3.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.wkb.custominputbox3.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true" >
        <uses-library android:name="android.test.runner" />

        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
            android:exported="true"
            android:theme="@style/WhiteBackgroundTheme" >
            <intent-filter android:priority="-100" >
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
            android:exported="true"
            android:theme="@style/WhiteBackgroundTheme" >
            <intent-filter android:priority="-100" >
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
            android:exported="true"
            android:theme="@style/WhiteBackgroundDialogTheme" >
            <intent-filter android:priority="-100" >
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.wkb.custominputbox3.test.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider>
    </application>

</manifest>