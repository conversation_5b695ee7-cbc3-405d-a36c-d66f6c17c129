<variant
    name="debug"
    package="com.wkb.custominputbox3"
    minSdkVersion="16"
    targetSdkVersion="33"
    debuggable="true"
    mergedManifest="build/intermediates/merged_manifest/debug/AndroidManifest.xml"
    manifestMergeReport="build/outputs/logs/manifest-merger-debug-report.txt"
    proguardFiles="build/intermediates/default_proguard_files/global/proguard-android.txt-7.2.2"
    partialResultsDir="build/intermediates/lint_partial_results/debug/out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifest="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/main/kotlin"
        resDirectories="src/main/res"
        assetsDirectories="src/main/assets"/>
    <sourceProvider
        manifest="src/debug/AndroidManifest.xml"
        javaDirectories="src/debug/java:src/debug/kotlin"
        resDirectories="src/debug/res"
        assetsDirectories="src/debug/assets"/>
  </sourceProviders>
  <testSourceProviders>
    <sourceProvider
        manifest="src/test/AndroidManifest.xml"
        javaDirectories="src/test/java:src/test/kotlin"
        resDirectories="src/test/res"
        assetsDirectories="src/test/assets"
        unitTest="true"/>
    <sourceProvider
        manifest="src/testDebug/AndroidManifest.xml"
        javaDirectories="src/testDebug/java:src/testDebug/kotlin"
        resDirectories="src/testDebug/res"
        assetsDirectories="src/testDebug/assets"
        unitTest="true"/>
    <sourceProvider
        manifest="src/androidTest/AndroidManifest.xml"
        javaDirectories="src/androidTest/java:src/androidTest/kotlin"
        resDirectories="src/androidTest/res"
        assetsDirectories="src/androidTest/assets"
        androidTest="true"/>
    <sourceProvider
        manifest="src/androidTestDebug/AndroidManifest.xml"
        javaDirectories="src/androidTestDebug/java:src/androidTestDebug/kotlin"
        resDirectories="src/androidTestDebug/res"
        assetsDirectories="src/androidTestDebug/assets"
        androidTest="true"/>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <mainArtifact
      classOutputs="build/intermediates/javac/debug/classes:build/intermediates/compile_r_class_jar/debug/R.jar"
      applicationId="com.wkb.custominputbox3"
      generatedSourceFolders="build/generated/source/buildConfig/debug:build/generated/aidl_source_output_dir/debug/out:build/generated/renderscript_source_output_dir/debug/out:build/generated/ap_generated_sources/debug/out"
      generatedResourceFolders="build/generated/res/rs/debug:build/generated/res/resValues/debug">
  </mainArtifact>
  <androidTestArtifact
      applicationId="com.wkb.custominputbox3.test"
      generatedResourceFolders="build/generated/res/rs/androidTest/debug:build/generated/res/resValues/androidTest/debug">
  </androidTestArtifact>
  <testArtifact>
  </testArtifact>
</variant>
