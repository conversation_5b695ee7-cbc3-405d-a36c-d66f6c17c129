<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <attr format="dimension" name="keyHeight">
        <enum name="match_parent" value="-1"/>
    </attr>
    <attr format="dimension" name="keyWidth">
        <enum name="match_parent" value="-1"/>
    </attr>
    <attr name="keyboardType">
        <enum name="integer" value="0"/>
        <enum name="decimal" value="1"/>
        <enum name="fingerprint" value="2"/>
        <enum name="custom" value="3"/>
        <enum name="four_columns" value="4"/>
    </attr>
    <string name="app_name">CustomInputBox</string>
    <string name="comma">,</string>
    <string name="dialog_btn_ok">Ok</string>
    <string name="dot">.</string>
    <string name="eight">8</string>
    <string name="error_min_value">This field must be greater than %1$s character.</string>
    <string name="error_regex">PLease enter valid user name.</string>
    <string name="five">5</string>
    <string name="four">4</string>
    <string name="minus">-</string>
    <string name="nine">9</string>
    <string name="ok">OK</string>
    <string name="one">1</string>
    <string name="robotoBold">Roboto-Bold.ttf</string>
    <string name="robotoRegular">Roboto-Regular.ttf</string>
    <string name="seven">7</string>
    <string name="six">6</string>
    <string name="something_wrong">Something went wrong, but we are fixing it. Please try
        again
        later
    </string>
    <string name="str_amount">Enter amount</string>
    <string name="str_credit_card">Enter your credit card number</string>
    <string name="str_password">Password</string>
    <string name="str_phone_number">Enter your phone number</string>
    <string name="str_title">Custom EditText</string>
    <string name="str_username">Username</string>
    <string name="three">3</string>
    <string name="two">2</string>
    <string name="zero">0</string>
    <style name="key" parent="keyNoBg">
        <item name="android:background">@drawable/key_bg</item>
    </style>
    <style name="keyContainer">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
    </style>
    <style name="keyNoBg">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:clickable">true</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:lineSpacingExtra">0dp</item>
        <item name="android:textSize">24sp</item>
        <item name="android:fontWeight" ns1:ignore="NewApi">700</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style>
    <declare-styleable name="CustomEditText">
        <attr name="android:inputType"/>
        <attr name="android:padding"/>
        <attr name="android:paddingLeft"/>
        <attr name="android:paddingRight"/>
        <attr name="android:paddingTop"/>
        <attr name="android:paddingBottom"/>
        <attr name="android:maxLength"/>
        <attr format="reference" name="edt_cursor"/>
        <attr format="color" name="edt_setBackgroundColor"/>
        <attr format="reference" name="edt_background"/>
        <attr format="color" name="edt_setBorderColor"/>
        <attr format="boolean" name="edt_setBorderView"/>
        <attr format="string" name="edt_setFont"/>
        <attr format="dimension" name="edt_setStrokeWidth"/>
        <attr format="dimension" name="edt_setCornerRadius"/>
        <attr format="boolean" name="edt_setClearIconVisible"/>
        <attr format="color" name="edt_clearIconTint"/>
        <attr format="color" name="edt_hideShowPasswordIconTint"/>
        <attr format="string" name="edt_setPrefix"/>
        <attr format="color" name="edt_setPrefixTextColor"/>
        <attr format="string" name="edt_minLength"/>
        <attr format="string" name="edt_regexp"/>
        <attr format="string" name="edt_pattern"/>
        <attr format="string" name="edt_specialChar"/>
        <attr format="boolean" name="edt_showPatternAsHint"/>
        <attr format="boolean" name="edt_multiLine"/>
        <attr format="integer" name="edt_maxCharCount"/>
        <attr format="color" name="edt_charCountTextColor"/>
        <attr format="dimension" name="edt_charCountTextSize"/>
        <attr format="boolean" name="edt_showCharCount"/>
    </declare-styleable>
    <declare-styleable name="NumberKeyboard">
        <attr name="keyboardType"/>
        <attr name="keyWidth"/>
        <attr name="keyHeight"/>
        <attr format="dimension" name="keyPadding"/>
        <attr format="reference" name="numberKeyBackground"/>
        <attr format="reference" name="numberKeyTextColor"/>
        <attr format="reference" name="leftAuxTextColor"/>
        <attr format="reference" name="leftAuxBtnIcon"/>
        <attr format="reference" name="leftAuxBtnBackground"/>
        <attr format="reference" name="rightAuxBtnIcon"/>
        <attr format="reference" name="rightAuxBtnBackground"/>
        <attr format="reference" name="layout"/>
    </declare-styleable>
</resources>