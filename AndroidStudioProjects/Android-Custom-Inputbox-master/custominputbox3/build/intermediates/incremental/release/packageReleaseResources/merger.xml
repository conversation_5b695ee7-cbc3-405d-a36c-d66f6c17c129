<?xml version="1.0" encoding="utf-8"?>
<merger version="3" xmlns:ns1="http://schemas.android.com/tools"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res"><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_fingerprint_normal.xml" preprocessing="true" qualifiers=""><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxxhdpi/ic_fingerprint_normal.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-mdpi/ic_fingerprint_normal.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-ldpi/ic_fingerprint_normal.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxhdpi/ic_fingerprint_normal.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-hdpi/ic_fingerprint_normal.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xhdpi/ic_fingerprint_normal.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-anydpi-v21/ic_fingerprint_normal.xml" qualifiers="anydpi-v21" type="drawable"/></file><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_comma_pressed.xml" preprocessing="true" qualifiers=""><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxxhdpi/ic_comma_pressed.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-mdpi/ic_comma_pressed.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-ldpi/ic_comma_pressed.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxhdpi/ic_comma_pressed.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-hdpi/ic_comma_pressed.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xhdpi/ic_comma_pressed.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-anydpi-v21/ic_comma_pressed.xml" qualifiers="anydpi-v21" type="drawable"/></file><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_check_circle_pressed.xml" preprocessing="true" qualifiers=""><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxxhdpi/ic_check_circle_pressed.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-mdpi/ic_check_circle_pressed.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-ldpi/ic_check_circle_pressed.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxhdpi/ic_check_circle_pressed.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-hdpi/ic_check_circle_pressed.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xhdpi/ic_check_circle_pressed.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-anydpi-v21/ic_check_circle_pressed.xml" qualifiers="anydpi-v21" type="drawable"/></file><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_backspace_pressed.xml" preprocessing="true" qualifiers=""><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxxhdpi/ic_backspace_pressed.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-mdpi/ic_backspace_pressed.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-ldpi/ic_backspace_pressed.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxhdpi/ic_backspace_pressed.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-hdpi/ic_backspace_pressed.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xhdpi/ic_backspace_pressed.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-anydpi-v24/ic_backspace_pressed.xml" qualifiers="anydpi-v24" type="drawable"/></file><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_comma_normal.xml" preprocessing="true" qualifiers=""><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxxhdpi/ic_comma_normal.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-mdpi/ic_comma_normal.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-ldpi/ic_comma_normal.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxhdpi/ic_comma_normal.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-hdpi/ic_comma_normal.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xhdpi/ic_comma_normal.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-anydpi-v21/ic_comma_normal.xml" qualifiers="anydpi-v21" type="drawable"/></file><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_check_circle_normal.xml" preprocessing="true" qualifiers=""><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxxhdpi/ic_check_circle_normal.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-mdpi/ic_check_circle_normal.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-ldpi/ic_check_circle_normal.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxhdpi/ic_check_circle_normal.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-hdpi/ic_check_circle_normal.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xhdpi/ic_check_circle_normal.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-anydpi-v21/ic_check_circle_normal.xml" qualifiers="anydpi-v21" type="drawable"/></file><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_fingerprint_pressed.xml" preprocessing="true" qualifiers=""><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxxhdpi/ic_fingerprint_pressed.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-mdpi/ic_fingerprint_pressed.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-ldpi/ic_fingerprint_pressed.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxhdpi/ic_fingerprint_pressed.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-hdpi/ic_fingerprint_pressed.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xhdpi/ic_fingerprint_pressed.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-anydpi-v21/ic_fingerprint_pressed.xml" qualifiers="anydpi-v21" type="drawable"/></file><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_backspace_normal.xml" preprocessing="true" qualifiers=""><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxxhdpi/ic_backspace_normal.png" qualifiers="xxxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-mdpi/ic_backspace_normal.png" qualifiers="mdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-ldpi/ic_backspace_normal.png" qualifiers="ldpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xxhdpi/ic_backspace_normal.png" qualifiers="xxhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-hdpi/ic_backspace_normal.png" qualifiers="hdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-xhdpi/ic_backspace_normal.png" qualifiers="xhdpi-v4" type="drawable"/><generated-file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/pngs/release/drawable-anydpi-v24/ic_backspace_normal.xml" qualifiers="anydpi-v24" type="drawable"/></file></source><source path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/rs/release"/><source path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res"><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values-v26/styles.xml" qualifiers="v26"><style name="keyNoBg">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:clickable">true</item>


        <item name="android:includeFontPadding">false</item>
        <item name="android:lineSpacingExtra">0dp</item>
        <item name="android:textSize">25sp</item>
        <item name="android:fontWeight">700</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style><style name="key" parent="keyNoBg">
        <item name="android:background">@drawable/key_bg</item>
    </style><style name="keyContainer">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
    </style></file><file name="ic_backspace" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_backspace.xml" qualifiers="" type="drawable"/><file name="key_bg_pressed" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg_pressed.xml" qualifiers="" type="drawable"/><file name="key_bg_transparent" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg_transparent.xml" qualifiers="" type="drawable"/><file name="ic_check_circle" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_check_circle.xml" qualifiers="" type="drawable"/><file name="key_bg" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg.xml" qualifiers="" type="drawable"/><file name="key_text_color" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_text_color.xml" qualifiers="" type="drawable"/><file name="key_text_color2" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_text_color2.xml" qualifiers="" type="drawable"/><file name="ic_fingerprint" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_fingerprint.xml" qualifiers="" type="drawable"/><file name="key_bg_normal" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg_normal.xml" qualifiers="" type="drawable"/><file name="ic_comma" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_comma.xml" qualifiers="" type="drawable"/><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml" qualifiers=""><style name="keyNoBg">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:clickable">true</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:lineSpacingExtra">0dp</item>
        <item name="android:textSize">24sp</item>
        <item name="android:fontWeight" ns1:ignore="NewApi">700</item>
        <item name="android:fontFamily">@font/roboto_regular</item>
    </style><style name="key" parent="keyNoBg">
        <item name="android:background">@drawable/key_bg</item>
    </style><style name="keyContainer">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
    </style></file><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml" qualifiers=""><string name="app_name">CustomInputBox</string><string name="robotoBold">Roboto-Bold.ttf</string><string name="robotoRegular">Roboto-Regular.ttf</string><string name="dialog_btn_ok">Ok</string><string name="something_wrong">Something went wrong, but we are fixing it. Please try
        again
        later
    </string><string name="error_min_value">This field must be greater than %1$s character.</string><string name="error_regex">PLease enter valid user name.</string><string name="str_title">Custom EditText</string><string name="str_phone_number">Enter your phone number</string><string name="str_credit_card">Enter your credit card number</string><string name="str_username">Username</string><string name="str_password">Password</string><string name="str_amount">Enter amount</string><string name="zero">0</string><string name="one">1</string><string name="two">2</string><string name="three">3</string><string name="four">4</string><string name="five">5</string><string name="six">6</string><string name="seven">7</string><string name="eight">8</string><string name="nine">9</string><string name="minus">-</string><string name="comma">,</string><string name="dot">.</string><string name="ok">OK</string></file><file path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/attrs.xml" qualifiers=""><declare-styleable name="CustomEditText">
        <attr name="android:inputType"/>
        <attr name="android:padding"/>
        <attr name="android:paddingLeft"/>
        <attr name="android:paddingRight"/>
        <attr name="android:paddingTop"/>
        <attr name="android:paddingBottom"/>
        <attr name="android:maxLength"/>
        <attr format="reference" name="edt_cursor"/>
        <attr format="color" name="edt_setBackgroundColor"/>
        <attr format="reference" name="edt_background"/>
        <attr format="color" name="edt_setBorderColor"/>
        <attr format="boolean" name="edt_setBorderView"/>
        <attr format="string" name="edt_setFont"/>
        <attr format="dimension" name="edt_setStrokeWidth"/>
        <attr format="dimension" name="edt_setCornerRadius"/>
        <attr format="boolean" name="edt_setClearIconVisible"/>
        <attr format="color" name="edt_clearIconTint"/>
        <attr format="color" name="edt_hideShowPasswordIconTint"/>
        <attr format="string" name="edt_setPrefix"/>
        <attr format="color" name="edt_setPrefixTextColor"/>
        <attr format="string" name="edt_minLength"/>
        <attr format="string" name="edt_regexp"/>
        <attr format="string" name="edt_pattern"/>
        <attr format="string" name="edt_specialChar"/>
        <attr format="boolean" name="edt_showPatternAsHint"/>
        <attr format="boolean" name="edt_multiLine"/>
        <attr format="integer" name="edt_maxCharCount"/>
        <attr format="color" name="edt_charCountTextColor"/>
        <attr format="dimension" name="edt_charCountTextSize"/>
        <attr format="boolean" name="edt_showCharCount"/>
    </declare-styleable><attr name="keyboardType">
        <enum name="integer" value="0"/>
        <enum name="decimal" value="1"/>
        <enum name="fingerprint" value="2"/>
        <enum name="custom" value="3"/>
        <enum name="four_columns" value="4"/>
    </attr><attr format="dimension" name="keyWidth">
        <enum name="match_parent" value="-1"/>
    </attr><attr format="dimension" name="keyHeight">
        <enum name="match_parent" value="-1"/>
    </attr><declare-styleable name="NumberKeyboard">
        <attr name="keyboardType"/>
        <attr name="keyWidth"/>
        <attr name="keyHeight"/>
        <attr format="dimension" name="keyPadding"/>
        <attr format="reference" name="numberKeyBackground"/>
        <attr format="reference" name="numberKeyTextColor"/>
        <attr format="reference" name="leftAuxTextColor"/>
        <attr format="reference" name="leftAuxBtnIcon"/>
        <attr format="reference" name="leftAuxBtnBackground"/>
        <attr format="reference" name="rightAuxBtnIcon"/>
        <attr format="reference" name="rightAuxBtnBackground"/>
        <attr format="reference" name="layout"/>
    </declare-styleable></file><file name="ic_visibility_on" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable-xhdpi/ic_visibility_on.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_visibility_off" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable-xhdpi/ic_visibility_off.png" qualifiers="xhdpi-v4" type="drawable"/><file name="ic_visibility_on" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable-xxhdpi/ic_visibility_on.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_visibility_off" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable-xxhdpi/ic_visibility_off.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="ic_visibility_on" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable-hdpi/ic_visibility_on.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_visibility_off" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable-hdpi/ic_visibility_off.png" qualifiers="hdpi-v4" type="drawable"/><file name="ic_visibility_on" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable-mdpi/ic_visibility_on.png" qualifiers="mdpi-v4" type="drawable"/><file name="ic_visibility_off" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable-mdpi/ic_visibility_off.png" qualifiers="mdpi-v4" type="drawable"/><file name="roboto_medium" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/font/roboto_medium.ttf" qualifiers="" type="font"/><file name="roboto_bold" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/font/roboto_bold.ttf" qualifiers="" type="font"/><file name="roboto_black" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/font/roboto_black.ttf" qualifiers="" type="font"/><file name="roboto_regular" path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/font/roboto_regular.ttf" qualifiers="" type="font"/></source><source path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/rs/release"/><source path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build/generated/res/resValues/release"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/release/res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="release" generated-set="release$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/release/res"/></dataSet><mergedItems><configuration qualifiers=""><declare-styleable name="CustomEditText">
        <attr name="android:inputType"/>
        <attr name="android:padding"/>
        <attr name="android:paddingLeft"/>
        <attr name="android:paddingRight"/>
        <attr name="android:paddingTop"/>
        <attr name="android:paddingBottom"/>
        <attr name="android:maxLength"/>
        <attr format="reference" name="edt_cursor"/>
        <attr format="color" name="edt_setBackgroundColor"/>
        <attr format="reference" name="edt_background"/>
        <attr format="color" name="edt_setBorderColor"/>
        <attr format="boolean" name="edt_setBorderView"/>
        <attr format="string" name="edt_setFont"/>
        <attr format="dimension" name="edt_setStrokeWidth"/>
        <attr format="dimension" name="edt_setCornerRadius"/>
        <attr format="boolean" name="edt_setClearIconVisible"/>
        <attr format="color" name="edt_clearIconTint"/>
        <attr format="color" name="edt_hideShowPasswordIconTint"/>
        <attr format="string" name="edt_setPrefix"/>
        <attr format="color" name="edt_setPrefixTextColor"/>
        <attr format="string" name="edt_minLength"/>
        <attr format="string" name="edt_regexp"/>
        <attr format="string" name="edt_pattern"/>
        <attr format="string" name="edt_specialChar"/>
        <attr format="boolean" name="edt_showPatternAsHint"/>
        <attr format="boolean" name="edt_multiLine"/>
        <attr format="integer" name="edt_maxCharCount"/>
        <attr format="color" name="edt_charCountTextColor"/>
        <attr format="dimension" name="edt_charCountTextSize"/>
        <attr format="boolean" name="edt_showCharCount"/>
    </declare-styleable><declare-styleable name="NumberKeyboard">
        <attr name="keyboardType"/>
        <attr name="keyWidth"/>
        <attr name="keyHeight"/>
        <attr format="dimension" name="keyPadding"/>
        <attr format="reference" name="numberKeyBackground"/>
        <attr format="reference" name="numberKeyTextColor"/>
        <attr format="reference" name="leftAuxTextColor"/>
        <attr format="reference" name="leftAuxBtnIcon"/>
        <attr format="reference" name="leftAuxBtnBackground"/>
        <attr format="reference" name="rightAuxBtnIcon"/>
        <attr format="reference" name="rightAuxBtnBackground"/>
        <attr format="reference" name="layout"/>
    </declare-styleable></configuration></mergedItems></merger>