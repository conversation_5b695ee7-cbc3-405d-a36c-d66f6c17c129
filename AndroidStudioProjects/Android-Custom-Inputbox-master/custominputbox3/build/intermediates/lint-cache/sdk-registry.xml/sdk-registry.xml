<?xml version="1.0" ?>
<sdk_metadata>
  <library  groupId="com.urbanairship.android" artifactId="urbanairship-sdk" recommended-version="9.3.1" >
    <versions from="9.1.0" status="deprecated" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-sdk:9.1.0" />
    <versions from="9.0.0" to="9.0.4" status="deprecated" description="Version is known issues with in-app automation. Please update to version &lt;current&gt; as soon as possible." />
    <versions from="8.9.1" to="8.9.2" status="deprecated" description="Version is known to have stability issues. Please update to version &lt;current&gt; as soon as possible. See &lt;url&gt; for more details." url="https://github.com/urbanairship/android-library/blob/master/CHANGELOG.md#version-893---october-17-2017" />
    <versions from="6.1.4" to="8.7.0" status="deprecated" description="Version is known to have Android O compatibility issues. Please update to version &lt;current&gt; as soon as possible." />
    <versions from="9.3.0" status="deprecated" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-sdk:9.3.0" />
    <versions from="9.3.1" status="recommended" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-sdk:9.3.1" />
  </library>
  <library  groupId="com.urbanairship.android" artifactId="urbanairship-core" recommended-version="9.3.1" >
    <versions from="9.1.0" status="deprecated" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-core:9.1.0" />
    <versions from="9.3.0" status="deprecated" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-core:9.3.0" />
    <versions from="9.3.1" status="recommended" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-core:9.3.1" />
  </library>
  <library  groupId="com.urbanairship.android" artifactId="urbanairship-fcm" recommended-version="9.3.1" >
    <versions from="9.1.0" status="deprecated" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-fcm:9.1.0" />
    <versions from="9.3.0" status="deprecated" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-fcm:9.3.0" />
    <versions from="9.3.1" status="recommended" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-fcm:9.3.1" />
  </library>
  <library  groupId="com.urbanairship.android" artifactId="urbanairship-gcm" recommended-version="9.3.1" >
    <versions from="9.1.0" status="deprecated" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-gcm:9.1.0" />
    <versions from="9.3.0" status="deprecated" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-gcm:9.3.0" />
    <versions from="9.3.1" status="recommended" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-gcm:9.3.1" />
  </library>
  <library  groupId="com.urbanairship.android" artifactId="urbanairship-adm" recommended-version="9.3.1" >
    <versions from="9.1.0" status="deprecated" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-adm:9.1.0" />
    <versions from="9.3.0" status="recommended" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-adm:9.3.0" />
    <versions from="9.3.1" status="recommended" version-literal="!SDK-VERSION-STRING!:com.urbanairship.android:urbanairship-adm:9.3.1" />
  </library>
  <library  groupId="com.tune" artifactId="tune-marketing-console-sdk" recommended-version="5.3.0" >
    <versions from="5.3.0" version-literal="com.tune.BuildConfig.5.3.0" description="Bug affecting SDK data quality" />
    <versions from="5.2.1" version-literal="com.tune.BuildConfig.5.2.1" />
    <versions from="5.2.0" version-literal="com.tune.BuildConfig.5.2.0" />
    <versions from="5.1.1" status="deprecated" version-literal="com.tune.BuildConfig.5.1.1" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="5.0.0" to="5.0.2" status="deprecated" version-literal="com.tune.BuildConfig.5.0.0" description="Bug affecting SDK data quality" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.15.2" to="4.16.0" status="deprecated" version-literal="com.tune.BuildConfig.4.15.2" description="Bug affecting SDK data quality" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.15.1" version-literal="com.tune.BuildConfig.4.15.1" description="Bug affecting SDK performance" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.15.0" version-literal="com.tune.BuildConfig.4.15.0" description="Bug affecting SDK performance" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.14.0" version-literal="com.tune.BuildConfig.4.14.0" description="Bug affecting SDK performance" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.13.0" status="deprecated" version-literal="com.tune.BuildConfig.4.13.0" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.12.1" status="deprecated" version-literal="com.tune.BuildConfig.4.12.1" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.12.0" status="deprecated" version-literal="com.tune.BuildConfig.4.12.0" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.11.1" status="deprecated" version-literal="com.tune.BuildConfig.4.11.1" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.11.0" status="deprecated" version-literal="com.tune.BuildConfig.4.11.0" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.10.2" version-literal="com.tune.BuildConfig.4.10.2" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.10.1" version-literal="com.tune.BuildConfig.4.10.1" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.10.0" version-literal="com.tune.BuildConfig.4.10.0" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.9.0" version-literal="com.tune.BuildConfig.4.9.0" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.8.0" to="4.8.1" version-literal="com.tune.BuildConfig.4.8.0" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="4.0.0" to="4.7.1" status="deprecated" version-literal="com.tune.BuildConfig.4.0.0" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="3.0.0" to="3.11.4" status="deprecated" version-literal="com.tune.BuildConfig.3.0.0" description="Bug affecting app stability" url="https://developers.tune.com/sdk/migrating-to-android-4-8-0-and-above/" />
    <versions from="0.0.0" to="3.0.0" status="deprecated" description="Bug affecting app stability" />
  </library>
  <library  groupId="com.android.volley" artifactId="volley" recommended-version="1.1.0" >
    <versions from="1.1.0" />
    <versions from="1.1.0-rc2" status="deprecated" description="Bug affecting app stability" url="https://github.com/google/volley/releases" />
    <versions from="1.1.0-rc1" status="deprecated" description="Bug affecting app stability" url="https://github.com/google/volley/releases" />
    <versions from="1.0.0" status="deprecated" description="Bug affecting app stability" url="https://github.com/google/volley/releases" />
  </library>
  <library groupId="com.crashlytics.sdk.android" artifactId="answers" recommended-version="17.4.0">
    <versions from="1.0.0" to="1.4.7" status="deprecated" description="Fabric Answers is deprecated. To continue getting real-time analytics, switch over to using Google Analytics.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/switch-to-analytics." url="https://firebase.google.com/docs/crashlytics/switch-to-analytics?platform=android" />
  </library>
  <library groupId="com.crashlytics.sdk.android" artifactId="crashlytics" recommended-version="17.2.2">
    <versions from="2.0.0" to="2.10.1" status="deprecated" description="The Fabric Crashlytics SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/upgrade-sdk" url="https://firebase.google.com/docs/crashlytics/upgrade-sdk?platform=android" />
  </library>
  <library groupId="com.crashlytics.sdk.android" artifactId="crashlytics-core" recommended-version="17.2.2">
    <versions from="2.0.0" to="2.7.0" status="deprecated" description="The Fabric Crashlytics SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/upgrade-sdk" url="https://firebase.google.com/docs/crashlytics/upgrade-sdk?platform=android" />
  </library>
  <library groupId="com.crashlytics.sdk.android" artifactId="crashlytics-ndk" recommended-version="17.2.2">
    <versions from="0.5.0" to="2.1.1" status="deprecated" description="The Fabric Crashlytics SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/upgrade-sdk" url="https://firebase.google.com/docs/crashlytics/upgrade-sdk?platform=android" />
  </library>
  <library  groupId="com.google.guava" artifactId="guava" recommended-version="24.1-jre" >
    <versions from="24.1-jre" />
  </library>
  <library  groupId="com.google.guava" artifactId="guava" recommended-version="25.0-android" >
    <versions from="25.0-android" />
    <versions from="24.1.1-android" status="insecure" description="Moderate security vulnerability" url="https://github.com/google/guava/wiki/CVE-2018-10237" />
    <versions from="24.1-android" status="deprecated" description="Bug affecting app performance" url="https://github.com/google/guava/releases" />
    <versions from="24.0-android" status="deprecated" description="Bug affecting app performance" url="https://github.com/google/guava/releases" />
  </library>
  <library  groupId="com.google.firebase" artifactId="firebase-analytics" recommended-version="15.0.0" >
    <versions from="15.0.0" />
    <versions from="12.0.1" />
    <versions from="12.0.0" />
    <versions from="11.8.0" />
    <versions from="11.6.2" />
    <versions from="11.6.0" />
    <versions from="11.4.2" />
    <versions from="11.4.0" />
    <versions from="11.2.2" />
    <versions from="11.2.0" />
    <versions from="11.0.4" />
    <versions from="11.0.2" />
    <versions from="11.0.1" />
    <versions from="11.0.0" />
    <versions from="10.2.6" />
    <versions from="10.2.4" />
    <versions from="10.2.1" />
    <versions from="10.2.0" />
    <versions from="10.0.1" />
    <versions from="10.0.0" />
    <versions from="9.8.0" />
    <versions from="9.6.1" />
    <versions from="9.6.0" />
    <versions from="9.4.0" />
    <versions from="9.2.1" />
    <versions from="9.2.0" />
    <versions from="9.0.2" />
    <versions from="9.0.1" />
    <versions from="9.0.0" />
  </library>
  <library  groupId="com.google.firebase" artifactId="firebase-appcheck-safetynet">
    <versions from="16.0.0-beta01" to="16.0.0-beta01" status="deprecated" description="The SafetyNet Attestation API is deprecated and has been replaced by the Play Integrity API. Before June 2023, migrate to the Firebase App Check SDK for Play Integrity by following https://firebase.google.com/docs/app-check/android/play-integrity-provider." url="https://developer.android.com/training/safetynet/deprecation-timeline"/>
    <versions from="16.0.0-beta02" to="16.0.0-beta02" status="deprecated" description="The SafetyNet Attestation API is deprecated and has been replaced by the Play Integrity API. Before June 2023, migrate to the Firebase App Check SDK for Play Integrity by following https://firebase.google.com/docs/app-check/android/play-integrity-provider." url="https://developer.android.com/training/safetynet/deprecation-timeline"/>
    <versions from="16.0.0-beta03" to="16.0.0-beta03" status="deprecated" description="The SafetyNet Attestation API is deprecated and has been replaced by the Play Integrity API. Before June 2023, migrate to the Firebase App Check SDK for Play Integrity by following https://firebase.google.com/docs/app-check/android/play-integrity-provider." url="https://developer.android.com/training/safetynet/deprecation-timeline"/>
    <versions from="16.0.0-beta04" to="16.0.0-beta04" status="deprecated" description="The SafetyNet Attestation API is deprecated and has been replaced by the Play Integrity API. Before June 2023, migrate to the Firebase App Check SDK for Play Integrity by following https://firebase.google.com/docs/app-check/android/play-integrity-provider." url="https://developer.android.com/training/safetynet/deprecation-timeline"/>
    <versions from="16.0.0-beta05" to="16.0.0-beta05" status="deprecated" description="The SafetyNet Attestation API is deprecated and has been replaced by the Play Integrity API. Before June 2023, migrate to the Firebase App Check SDK for Play Integrity by following https://firebase.google.com/docs/app-check/android/play-integrity-provider." url="https://developer.android.com/training/safetynet/deprecation-timeline"/>
    <versions from="16.0.0-beta06" to="16.0.0-beta06" status="deprecated" description="The SafetyNet Attestation API is deprecated and has been replaced by the Play Integrity API. Before June 2023, migrate to the Firebase App Check SDK for Play Integrity by following https://firebase.google.com/docs/app-check/android/play-integrity-provider." url="https://developer.android.com/training/safetynet/deprecation-timeline"/>
    <versions from="16.0.0" status="deprecated" description="The SafetyNet Attestation API is deprecated and has been replaced by the Play Integrity API. Before June 2023, migrate to the Firebase App Check SDK for Play Integrity by following https://firebase.google.com/docs/app-check/android/play-integrity-provider." url="https://developer.android.com/training/safetynet/deprecation-timeline"/>
  </library>
  <library groupId="com.google.firebase" artifactId="firebase-config">
    <versions from="21.1.0" to="21.1.0" status="deprecated" description="Bug affecting some locales causing HTTP errors" url="https://firebase.google.com/docs/remote-config/get-started?platform=android" />
  </library>
  <library groupId="com.google.firebase" artifactId="firebase-crashlytics" recommended-version="18.3.1">
    <versions from="17.0.0-beta01" to="17.0.0-beta01" status="deprecated" description="The Firebase Crashlytics beta SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the generally available Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/get-started" url="https://firebase.google.com/docs/crashlytics/get-started?platform=android" />
    <versions from="17.0.0-beta02" to="17.0.0-beta02" status="deprecated" description="The Firebase Crashlytics beta SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the generally available Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/get-started" url="https://firebase.google.com/docs/crashlytics/get-started?platform=android" />
    <versions from="17.0.0-beta03" to="17.0.0-beta03" status="deprecated" description="The Firebase Crashlytics beta SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the generally available Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/get-started" url="https://firebase.google.com/docs/crashlytics/get-started?platform=android" />
    <versions from="17.0.0-beta04" to="17.0.0-beta04" status="deprecated" description="The Firebase Crashlytics beta SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the generally available Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/get-started" url="https://firebase.google.com/docs/crashlytics/get-started?platform=android" />
    <versions from="18.3.0" to="18.3.0" status="deprecated" description="We detected an issue in this version of the Crashlytics Android SDK. We strongly recommend either using v18.2.13 or earlier or upgrading to v18.3.1+." url="https://github.com/firebase/firebase-android-sdk/issues/4223" />
  </library>
  <library groupId="com.google.firebase" artifactId="firebase-crashlytics-ndk" recommended-version="18.3.2">
    <versions from="17.0.0-beta01" to="17.0.0-beta01" status="deprecated" description="The Firebase Crashlytics beta SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the generally available Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/get-started" url="https://firebase.google.com/docs/crashlytics/get-started?platform=android" />
    <versions from="17.0.0-beta02" to="17.0.0-beta02" status="deprecated" description="The Firebase Crashlytics beta SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the generally available Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/get-started" url="https://firebase.google.com/docs/crashlytics/get-started?platform=android" />
    <versions from="17.0.0-beta03" to="17.0.0-beta03" status="deprecated" description="The Firebase Crashlytics beta SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the generally available Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/get-started" url="https://firebase.google.com/docs/crashlytics/get-started?platform=android" />
    <versions from="17.0.0-beta04" to="17.0.0-beta04" status="deprecated" description="The Firebase Crashlytics beta SDK is now deprecated and will continue reporting your app's crashes only until November 15, 2020. To continue getting crash reports in the Firebase console, make sure you upgrade to the generally available Firebase Crashlytics SDK version 17.0.0+.&#13;&#10;For more information, visit https://firebase.google.com/docs/crashlytics/get-started" url="https://firebase.google.com/docs/crashlytics/get-started?platform=android" />
    <versions from="18.3.0" to="18.3.0" status="deprecated" description="We detected an issue in this version of the Crashlytics Android SDK. We strongly recommend either using v18.2.13 or earlier or upgrading to v18.3.1+." url="https://github.com/firebase/firebase-android-sdk/issues/4223" />
    <versions from="18.3.1" to="18.3.1" status="deprecated" description="We detected an issue in this version of the Crashlytics Android SDK. We strongly recommend either using v18.2.13 or earlier or upgrading to v18.3.2+." url="https://github.com/firebase/firebase-android-sdk/issues/4313" />
  </library>
  <library  groupId="com.google.firebase" artifactId="firebase-messaging" recommended-version="20.1.2" >
    <versions from="20.1.1" status="deprecated" description="Bug affecting app stability" url="https://firebase.google.com/support/release-notes/android#messaging_v20-1-1" />
  </library>
  <library  groupId="com.google.firebase" artifactId="firebase-ml-vision">
    <versions from="15.0.0" to="24.1.0" status="deprecated" description="For more information, refer to documentation for specific features.&#13;&#10;Text recognition: https://firebase.google.com/docs/ml/android/recognize-text&#13;&#10;Image labeling: https://firebase.google.com/docs/ml/android/label-images&#13;&#10;Landmark recognition: https://firebase.google.com/docs/ml/android/recognize-landmarks " />
  </library>
  <library  groupId="com.google.firebase" artifactId="firebase-ml-model-interpreter">
    <versions from="15.0.0" to="22.0.4" status="deprecated" description="The Firebase ML Model Interpreter library is deprecated in favor of the Firebase ML Model Downloader library. For more information, visit https://firebase.google.com/docs/ml/android/use-custom-models" />
  </library>

  <library  groupId="com.google.firebase" artifactId="firebase-perf">
    <versions to="16.2.1" status="deprecated" description="This version will soon be deprecated. Update to version 19.0.10 or higher to get real time performance data. For more information, visit https://firebase.google.com/docs/perf-mon/troubleshooting" url="https://firebase.google.com/docs/perf-mon/troubleshooting?platform=android#faq-real-time-data"/>
  </library>

  <library  groupId="com.google.dagger" artifactId="dagger-android" recommended-version="2.35.1" >
    <versions from="2.15" />
    <versions from="2.14.1" status="deprecated" description="Bug affecting SDK performance" url="https://github.com/google/dagger/releases/tag/2.15" />
    <versions from="2.14" status="deprecated" description="Bug affecting SDK performance" url="https://github.com/google/dagger/releases/tag/2.15" />
    <versions from="2.13" status="deprecated" description="Bug affecting app performance" url="https://github.com/google/dagger/releases/tag/2.15" />
    <versions from="2.12" status="deprecated" description="Bug affecting app performance" url="https://github.com/google/dagger/releases/tag/2.15" />
    <versions from="2.11" status="deprecated" description="Bug affecting SDK performance" url="https://github.com/google/dagger/releases/tag/2.15" />
    <versions from="2.11-rc2" status="deprecated" description="Bug affecting SDK performance" url="https://github.com/google/dagger/releases/tag/2.15" />
    <versions from="2.11-rc1" status="deprecated" description="Bug affecting SDK performance" url="https://github.com/google/dagger/releases/tag/2.15" />
    <versions from="2.10" status="deprecated" url="https://github.com/google/dagger/releases/tag/2.15" />
  </library>
  <library  groupId="AppsFlyer" artifactId="af-android-sdk" recommended-version="4.8.10" >
    <versions from="4.8.9" version-literal="!SDK-VERSION-STRING!:com.appsflyer:af-android-sdk:4.8.9" />
    <versions from="4.8.10" version-literal="!SDK-VERSION-STRING!:com.appsflyer:af-android-sdk:4.8.10" />
  </library>
  <library  groupId="com.kochava" artifactId="tracker" recommended-version="3.4.0" >
    <versions from="3.4.0" />
    <versions from="3.3.1" version-literal="&quot;AndroidTracker 3.3.1&quot;, &quot;control.kochava.com&quot;" />
    <versions from="3.3.0" version-literal="&quot;AndroidTracker 3.3.0&quot;, &quot;control.kochava.com&quot;" />
    <versions from="3.2.0" version-literal="&quot;AndroidTracker 3.2.0&quot;, &quot;control.kochava.com&quot;" />
    <versions from="3.1.1" version-literal="&quot;AndroidTracker 3.1.1&quot;, &quot;control.kochava.com&quot;" />
    <versions from="3.1.0" status="deprecated" version-literal="&quot;AndroidTracker 3.1.0&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="3.0.0" status="deprecated" version-literal="&quot;AndroidTracker 3.0.0&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20170303" status="deprecated" version-literal="&quot;Android20170303&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20170216" status="deprecated" version-literal="&quot;Android20170216&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20170127" status="deprecated" version-literal="&quot;Android20170127&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20170106" status="deprecated" version-literal="&quot;Android20170106&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20161122" status="deprecated" version-literal="&quot;Android20161122&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20160914" status="deprecated" version-literal="&quot;Android20160914&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20160902" status="deprecated" version-literal="&quot;Android20160902&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20160811" status="deprecated" version-literal="&quot;Android20160811&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20160615" status="deprecated" version-literal="&quot;Android20160615&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20160427" status="deprecated" version-literal="&quot;Android20160427&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20160222" status="deprecated" version-literal="&quot;Android20160222&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20151109" status="deprecated" version-literal="&quot;Android20151109&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20150511" status="deprecated" version-literal="&quot;Android20150511&quot;, &quot;control.kochava.com&quot;" description="Bug affecting SDK performance" />
    <versions from="20150312" status="insecure" version-literal="&quot;Android20150312&quot;, &quot;control.kochava.com&quot;" description="Moderate security vulnerability" />
    <versions from="20150128" status="insecure" version-literal="&quot;Android20150128&quot;, &quot;control.kochava.com&quot;" description="Moderate security vulnerability" />
    <versions from="20141023" status="insecure" version-literal="&quot;Android20141023&quot;, &quot;control.kochava.com&quot;" description="Moderate security vulnerability" />
    <versions from="20140825" status="insecure" version-literal="&quot;Android20140825&quot;, &quot;control.kochava.com&quot;" description="Moderate security vulnerability" />
  </library>
</sdk_metadata>
