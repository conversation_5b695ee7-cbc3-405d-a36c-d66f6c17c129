<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 7.2.2" type="incidents">

    <incident
        id="ButtonCase"
        severity="warning"
        message="The standard Android way to capitalize Ok is &quot;OK&quot; (tip: use `@android:string/ok` instead)">
        <location
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="34"
            startOffset="213"
            endLine="5"
            endColumn="36"
            endOffset="215"/>
    </incident>

    <incident
        id="Typos"
        severity="warning"
        message="&quot;Ok&quot; is usually capitalized as &quot;OK&quot;">
        <fix-alternatives>
            <fix-replace
                description="Replace with &quot;OK&quot;"
                oldString="Ok"
                replacement="OK"/>
        </fix-alternatives>
        <location
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="5"
            column="34"
            startOffset="213"
            endLine="5"
            endColumn="34"
            endOffset="215"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` should call `View#performClick` when a click is detected">
        <location
            file="${:custominputbox3*debug*sourceProvider*0*javaDir*0}/com/wkb/custominputbox3/CustomEditText.java"
            line="269"
            column="28"
            startOffset="10356"
            endLine="269"
            endColumn="35"
            endOffset="10363"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 16">
        <fix-data conditional="true"/>
        <location
            file="${:custominputbox3*debug*sourceProvider*0*javaDir*0}/com/wkb/custominputbox3/CustomEditText.java"
            line="355"
            column="13"
            startOffset="14015"
            endLine="355"
            endColumn="68"
            endOffset="14070"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:custominputbox3*debug*sourceProvider*0*javaDir*0}/com/wkb/custominputbox3/CustomEditText.java"
            line="513"
            column="53"
            startOffset="19853"
            endLine="513"
            endColumn="66"
            endOffset="19866"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view `MultiLineEditText` overrides `onTouchEvent` but not `performClick`">
        <location
            file="${:custominputbox3*debug*sourceProvider*0*javaDir*0}/com/wkb/custominputbox3/MultiLineEditText.java"
            line="578"
            column="20"
            startOffset="20575"
            endLine="578"
            endColumn="32"
            endOffset="20587"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="`onTouch` should call `View#performClick` when a click is detected">
        <location
            file="${:custominputbox3*debug*sourceProvider*0*javaDir*0}/com/wkb/custominputbox3/MultiLineEditText.java"
            line="241"
            column="28"
            startOffset="9033"
            endLine="241"
            endColumn="35"
            endOffset="9040"/>
    </incident>

    <incident
        id="CustomViewStyleable"
        severity="warning"
        message="By convention, the custom view (`MultiLineEditText`) and the declare-styleable (`CustomEditText`) should have the same name (various editor features rely on this convention)">
        <location
            file="${:custominputbox3*debug*sourceProvider*0*javaDir*0}/com/wkb/custominputbox3/MultiLineEditText.java"
            line="100"
            column="82"
            startOffset="3360"
            endLine="100"
            endColumn="108"
            endOffset="3386"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``AppCompatEditText`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:custominputbox3*debug*sourceProvider*0*javaDir*0}/com/wkb/custominputbox3/MultiLineEditText.java"
            line="239"
            column="9"
            startOffset="8932"
            endLine="251"
            endColumn="11"
            endOffset="9592"/>
    </incident>

    <incident
        id="ClickableViewAccessibility"
        severity="warning"
        message="Custom view ``AppCompatEditText`` has `setOnTouchListener` called on it but does not override `performClick`">
        <location
            file="${:custominputbox3*debug*sourceProvider*0*javaDir*0}/com/wkb/custominputbox3/MultiLineEditText.java"
            line="481"
            column="9"
            startOffset="17912"
            endLine="481"
            endColumn="46"
            endOffset="17949"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldString="33"
            replacement="36"/>
        <location
            file="${:custominputbox3*projectDir}/build.gradle"
            line="9"
            column="9"
            startOffset="157"
            endLine="9"
            endColumn="28"
            endOffset="176"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1">
        <fix-replace
            description="Change to 1.7.1"
            family="Update versions"
            oldString="1.6.1"
            replacement="1.7.1"/>
        <location
            file="${:custominputbox3*projectDir}/build.gradle"
            line="27"
            column="20"
            startOffset="595"
            endLine="27"
            endColumn="56"
            endOffset="631"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.core:core than 1.10.1 is available: 1.16.0">
        <fix-replace
            description="Change to 1.16.0"
            family="Update versions"
            oldString="1.10.1"
            replacement="1.16.0"/>
        <location
            file="${:custominputbox3*projectDir}/build.gradle"
            line="28"
            column="20"
            startOffset="651"
            endLine="28"
            endColumn="47"
            endOffset="678"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.3.0">
        <fix-replace
            description="Change to 1.3.0"
            family="Update versions"
            oldString="1.1.5"
            replacement="1.3.0"/>
        <location
            file="${:custominputbox3*projectDir}/build.gradle"
            line="31"
            column="31"
            startOffset="758"
            endLine="31"
            endColumn="62"
            endOffset="789"/>
    </incident>

    <incident
        id="GradleDependency"
        severity="warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.7.0">
        <fix-replace
            description="Change to 3.7.0"
            family="Update versions"
            oldString="3.5.1"
            replacement="3.7.0"/>
        <location
            file="${:custominputbox3*projectDir}/build.gradle"
            line="32"
            column="31"
            startOffset="820"
            endLine="32"
            endColumn="75"
            endOffset="864"/>
    </incident>

</incidents>
