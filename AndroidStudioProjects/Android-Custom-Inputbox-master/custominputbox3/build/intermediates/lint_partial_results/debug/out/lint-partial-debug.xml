<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 7.2.2" type="partial_results">
    <map id="VectorDrawableCompat">
        <entry
            name="ic_backspace_normal"
            boolean="false"/>
        <entry
            name="ic_check_circle_normal"
            boolean="false"/>
        <entry
            name="ic_comma_normal"
            boolean="false"/>
        <entry
            name="ic_backspace_pressed"
            boolean="false"/>
        <entry
            name="ic_check_circle_pressed"
            boolean="false"/>
        <entry
            name="ic_fingerprint_normal"
            boolean="false"/>
        <entry
            name="ic_comma_pressed"
            boolean="false"/>
        <entry
            name="ic_fingerprint_pressed"
            boolean="false"/>
    </map>
    <map id="AppBundleLocaleChanges">
        <location id="localeChangeLocation"
            file="${:custominputbox3*debug*sourceProvider*0*javaDir*0}/com/wkb/custominputbox3/utils/LocaleHelper.java"
            line="75"
            column="23"
            startOffset="2508"
            endLine="75"
            endColumn="29"
            endOffset="2514"/>
    </map>
    <map id="UnusedResources">
        <location id="R.drawable.ic_comma"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_comma.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="12"
            endOffset="274"/>
        <location id="R.drawable.ic_check_circle"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_check_circle.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="12"
            endOffset="286"/>
        <location id="R.drawable.ic_comma_pressed"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_comma_pressed.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="531"/>
        <location id="R.drawable.ic_fingerprint"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_fingerprint.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="12"
            endOffset="286"/>
        <location id="R.string.comma"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="29"
            column="13"
            startOffset="1266"
            endLine="29"
            endColumn="25"
            endOffset="1278"/>
        <location id="R.string.str_phone_number"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="13"
            column="13"
            startOffset="585"
            endLine="13"
            endColumn="36"
            endOffset="608"/>
        <location id="R.string.six"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="24"
            column="13"
            startOffset="1089"
            endLine="24"
            endColumn="23"
            endOffset="1099"/>
        <location id="R.drawable.key_bg_normal"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/key_bg_normal.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="7"
            endColumn="9"
            endOffset="230"/>
        <location id="R.string.one"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="19"
            column="13"
            startOffset="915"
            endLine="19"
            endColumn="23"
            endOffset="925"/>
        <location id="R.drawable.key_bg"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/key_bg.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="12"
            endOffset="270"/>
        <location id="R.string.app_name"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="2"
            column="13"
            startOffset="24"
            endLine="2"
            endColumn="28"
            endOffset="39"/>
        <location id="R.drawable.ic_fingerprint_pressed"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_fingerprint_pressed.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="2411"/>
        <location id="R.drawable.ic_backspace_normal"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_backspace_normal.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="30"
            endColumn="10"
            endOffset="1389"/>
        <location id="R.string.ok"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="31"
            column="13"
            startOffset="1336"
            endLine="31"
            endColumn="22"
            endOffset="1345"/>
        <location id="R.string.robotoBold"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="3"
            column="13"
            startOffset="76"
            endLine="3"
            endColumn="30"
            endOffset="93"/>
        <location id="R.string.robotoRegular"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="4"
            column="13"
            startOffset="131"
            endLine="4"
            endColumn="33"
            endOffset="151"/>
        <entry
            name="model"
            string="attr[edt_multiLine(D),android_maxLength(D),edt_setFont(D),edt_setPrefixTextColor(D),keyHeight(D),rightAuxBtnBackground(D),edt_clearIconTint(D),edt_showCharCount(D),leftAuxTextColor(D),edt_setBorderView(D),edt_charCountTextSize(D),numberKeyTextColor(D),keyWidth(D),android_inputType(D),edt_specialChar(D),edt_setPrefix(D),edt_pattern(D),edt_hideShowPasswordIconTint(D),edt_minLength(D),leftAuxBtnIcon(D),edt_background(D),android_paddingBottom(D),edt_setBackgroundColor(D),edt_setBorderColor(D),android_paddingLeft(D),edt_cursor(D),rightAuxBtnIcon(D),edt_regexp(D),android_paddingTop(D),edt_setCornerRadius(D),android_paddingRight(D),edt_setStrokeWidth(D),layout(D),edt_showPatternAsHint(D),keyboardType(D),edt_setClearIconVisible(D),edt_charCountTextColor(D),numberKeyBackground(D),leftAuxBtnBackground(D),edt_maxCharCount(D),keyPadding(D),android_padding(D)],drawable[ic_backspace_normal(D),ic_check_circle_normal(D),ic_backspace(D),ic_check_circle(D),ic_comma_normal(D),ic_backspace_pressed(D),ic_check_circle_pressed(D),ic_comma_pressed(D),ic_fingerprint_pressed(D),ic_fingerprint(D),key_bg_normal(D),key_text_color(D),ic_comma(D),key_text_color2(D),ic_visibility_on(U),ic_fingerprint_normal(D),key_bg(D),key_bg_transparent(D),key_bg_pressed(D),ic_visibility_off(U)],font[roboto_regular(E),roboto_medium(E)],string[nine(D),minus(D),dialog_btn_ok(U),dot(D),seven(D),error_min_value(U),something_wrong(U),two(D),three(D),eight(D),zero(D),str_username(D),str_amount(D),four(D),ok(D),five(D),str_phone_number(D),six(D),one(D),app_name(D),str_credit_card(D),comma(D),error_regex(U),robotoRegular(D),str_title(D),str_password(D),robotoBold(D)],style[keyContainer(D),keyNoBg(D),key(D)],styleable[CustomEditText_edt_charCountTextSize(R),CustomEditText_edt_setBackgroundColor(R),NumberKeyboard(D),CustomEditText_edt_showPatternAsHint(R),CustomEditText_edt_setStrokeWidth(R),CustomEditText_edt_minLength(R),CustomEditText_android_paddingLeft(R),CustomEditText_edt_maxCharCount(R),CustomEditText_edt_setPrefix(R),CustomEditText_android_paddingRight(R),CustomEditText_edt_cursor(R),CustomEditText_edt_showCharCount(R),CustomEditText_edt_setPrefixTextColor(R),CustomEditText_edt_setBorderView(R),CustomEditText_edt_hideShowPasswordIconTint(R),CustomEditText(U),CustomEditText_edt_setCornerRadius(R),CustomEditText_android_paddingTop(R),CustomEditText_edt_clearIconTint(R),CustomEditText_android_padding(R),CustomEditText_edt_setClearIconVisible(R),CustomEditText_edt_setBorderColor(R),CustomEditText_edt_pattern(R),CustomEditText_edt_specialChar(R),CustomEditText_android_paddingBottom(R),CustomEditText_edt_charCountTextColor(R),CustomEditText_edt_regexp(R),CustomEditText_edt_multiLine(R),CustomEditText_edt_setFont(R)];2c^2f^2a,2d^30^2b,33^32^39,36^31^2e,3a^3c^34,5c^3e^3f,5d^5c^3a;;;"/>
        <location id="R.string.str_password"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="16"
            column="13"
            startOffset="778"
            endLine="16"
            endColumn="32"
            endOffset="797"/>
        <location id="R.drawable.ic_backspace_pressed"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_backspace_pressed.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="31"
            endColumn="10"
            endOffset="1411"/>
        <location id="R.string.two"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="20"
            column="13"
            startOffset="949"
            endLine="20"
            endColumn="23"
            endOffset="959"/>
        <location id="R.string.dot"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="30"
            column="13"
            startOffset="1302"
            endLine="30"
            endColumn="23"
            endOffset="1312"/>
        <location id="R.string.eight"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="26"
            column="13"
            startOffset="1159"
            endLine="26"
            endColumn="25"
            endOffset="1171"/>
        <location id="R.string.four"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="22"
            column="13"
            startOffset="1019"
            endLine="22"
            endColumn="24"
            endOffset="1030"/>
        <location id="R.string.three"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="21"
            column="13"
            startOffset="983"
            endLine="21"
            endColumn="25"
            endOffset="995"/>
        <location id="R.drawable.key_bg_pressed"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/key_bg_pressed.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="9"
            endOffset="179"/>
        <location id="R.drawable.key_text_color2"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/key_text_color2.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="12"
            endOffset="221"/>
        <location id="R.style.keyNoBg"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/styles.xml"
            line="4"
            column="12"
            startOffset="110"
            endLine="4"
            endColumn="26"
            endOffset="124"/>
        <location id="R.drawable.ic_comma_normal"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_comma_normal.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="531"/>
        <location id="R.string.zero"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="18"
            column="13"
            startOffset="880"
            endLine="18"
            endColumn="24"
            endOffset="891"/>
        <location id="R.drawable.ic_backspace"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_backspace.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="6"
            endColumn="12"
            endOffset="282"/>
        <location id="R.drawable.key_text_color"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/key_text_color.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="5"
            endColumn="12"
            endOffset="221"/>
        <location id="R.string.str_amount"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="17"
            column="13"
            startOffset="828"
            endLine="17"
            endColumn="30"
            endOffset="845"/>
        <location id="R.drawable.key_bg_transparent"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/key_bg_transparent.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="2"
            endColumn="9"
            endOffset="47"/>
        <location id="R.string.str_credit_card"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="14"
            column="13"
            startOffset="654"
            endLine="14"
            endColumn="35"
            endOffset="676"/>
        <location id="R.drawable.ic_check_circle_pressed"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_check_circle_pressed.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="5"
            endColumn="10"
            endOffset="394"/>
        <location id="R.string.minus"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="28"
            column="13"
            startOffset="1230"
            endLine="28"
            endColumn="25"
            endOffset="1242"/>
        <location id="R.style.keyContainer"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/styles.xml"
            line="20"
            column="12"
            startOffset="862"
            endLine="20"
            endColumn="31"
            endOffset="881"/>
        <location id="R.drawable.ic_fingerprint_normal"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_fingerprint_normal.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="10"
            endColumn="10"
            endOffset="2413"/>
        <location id="R.string.str_title"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="12"
            column="13"
            startOffset="531"
            endLine="12"
            endColumn="29"
            endOffset="547"/>
        <location id="R.string.str_username"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="15"
            column="13"
            startOffset="728"
            endLine="15"
            endColumn="32"
            endOffset="747"/>
        <location id="R.string.five"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="23"
            column="13"
            startOffset="1054"
            endLine="23"
            endColumn="24"
            endOffset="1065"/>
        <location id="R.style.key"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/styles.xml"
            line="16"
            column="12"
            startOffset="744"
            endLine="16"
            endColumn="22"
            endOffset="754"/>
        <location id="R.string.nine"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="27"
            column="13"
            startOffset="1195"
            endLine="27"
            endColumn="24"
            endOffset="1206"/>
        <location id="R.string.seven"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/values/strings.xml"
            line="25"
            column="13"
            startOffset="1123"
            endLine="25"
            endColumn="25"
            endOffset="1135"/>
        <location id="R.drawable.ic_check_circle_normal"
            file="${:custominputbox3*debug*sourceProvider*0*resDir*0}/drawable/ic_check_circle_normal.xml"
            line="1"
            column="1"
            startOffset="0"
            endLine="5"
            endColumn="10"
            endOffset="394"/>
    </map>

</incidents>
