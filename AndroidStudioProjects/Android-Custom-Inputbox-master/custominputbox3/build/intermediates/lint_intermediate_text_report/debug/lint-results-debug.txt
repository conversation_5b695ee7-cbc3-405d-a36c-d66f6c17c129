/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java:100: Warning: By convention, the custom view (MultiLineEditText) and the declare-styleable (CustomEditText) should have the same name (various editor features rely on this convention) [CustomViewStyleable]
        android.content.res.TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.CustomEditText);
                                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "CustomViewStyleable":
   The convention for custom views is to use a declare-styleable whose name
   matches the custom view class name. The IDE relies on this convention such
   that for example code completion can be offered for attributes in a custom
   view in layout XML resource files.

   (Similarly, layout parameter classes should use the suffix _Layout.)

/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle:9: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdkVersion 33
        ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/utils/LocaleHelper.java:75: Warning: Found dynamic locale changes, but did not find corresponding Play Core library calls for downloading languages and splitting by language is not disabled in the bundle configuration [AppBundleLocaleChanges]
        configuration.locale = locale;
                      ~~~~~~

   Explanation for issues of type "AppBundleLocaleChanges":
   When changing locales at runtime (e.g. to provide an in-app language
   switcher), the Android App Bundle must be configured to not split by locale
   or the Play Core library must be used to download additional locales at
   runtime.

   https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes

/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle:27: Warning: A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1 [GradleDependency]
    implementation 'androidx.appcompat:appcompat:1.6.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle:28: Warning: A newer version of androidx.core:core than 1.10.1 is available: 1.16.0 [GradleDependency]
    implementation 'androidx.core:core:1.10.1'
                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle:31: Warning: A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.3.0 [GradleDependency]
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle:32: Warning: A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.7.0 [GradleDependency]
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "GradleDependency":
   This detector looks for usages of libraries where the version you are using
   is not the current stable release. Using older versions is fine, and there
   are cases where you deliberately want to stick with an older version.
   However, you may simply not be aware that a more recent version is
   available, and that is what this lint check helps find.

/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/CustomEditText.java:513: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
            colorAttr = getContext().getResources().getIdentifier("colorAccent", "attr", getContext().getPackageName());
                                                    ~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedApi":
   Discouraged APIs are allowed and are not deprecated, but they may be unfit
   for common use (e.g. due to slow performance or subtle behavior).

/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:5: Warning: "Ok" is usually capitalized as "OK" [Typos]
    <string name="dialog_btn_ok">Ok</string>
                                 ^

   Explanation for issues of type "Typos":
   This check looks through the string definitions, and if it finds any words
   that look like likely misspellings, they are flagged.

/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/CustomEditText.java:355: Warning: Unnecessary; SDK_INT is always >= 16 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_backspace.xml:2: Warning: The resource R.drawable.ic_backspace appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_backspace_normal.xml:2: Warning: The resource R.drawable.ic_backspace_normal appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_backspace_pressed.xml:2: Warning: The resource R.drawable.ic_backspace_pressed appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_check_circle.xml:2: Warning: The resource R.drawable.ic_check_circle appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_check_circle_normal.xml:1: Warning: The resource R.drawable.ic_check_circle_normal appears to be unused [UnusedResources]
<vector android:height="40dp" android:tint="#3DB6AC"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_check_circle_pressed.xml:1: Warning: The resource R.drawable.ic_check_circle_pressed appears to be unused [UnusedResources]
<vector android:height="40dp" android:tint="#3DB6AC"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_comma.xml:2: Warning: The resource R.drawable.ic_comma appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_comma_normal.xml:2: Warning: The resource R.drawable.ic_comma_normal appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_comma_pressed.xml:2: Warning: The resource R.drawable.ic_comma_pressed appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_fingerprint.xml:2: Warning: The resource R.drawable.ic_fingerprint appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_fingerprint_normal.xml:2: Warning: The resource R.drawable.ic_fingerprint_normal appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_fingerprint_pressed.xml:2: Warning: The resource R.drawable.ic_fingerprint_pressed appears to be unused [UnusedResources]
<vector xmlns:android="http://schemas.android.com/apk/res/android"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg.xml:2: Warning: The resource R.drawable.key_bg appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg_normal.xml:2: Warning: The resource R.drawable.key_bg_normal appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg_pressed.xml:2: Warning: The resource R.drawable.key_bg_pressed appears to be unused [UnusedResources]
<shape xmlns:android="http://schemas.android.com/apk/res/android"
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg_transparent.xml:2: Warning: The resource R.drawable.key_bg_transparent appears to be unused [UnusedResources]
<shape/>
~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_text_color.xml:2: Warning: The resource R.drawable.key_text_color appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_text_color2.xml:2: Warning: The resource R.drawable.key_text_color2 appears to be unused [UnusedResources]
<selector xmlns:android="http://schemas.android.com/apk/res/android">
^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:2: Warning: The resource R.string.app_name appears to be unused [UnusedResources]
    <string name="app_name">CustomInputBox</string>
            ~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:3: Warning: The resource R.string.robotoBold appears to be unused [UnusedResources]
    <string name="robotoBold">Roboto-Bold.ttf</string>
            ~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:4: Warning: The resource R.string.robotoRegular appears to be unused [UnusedResources]
    <string name="robotoRegular">Roboto-Regular.ttf</string>
            ~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:12: Warning: The resource R.string.str_title appears to be unused [UnusedResources]
    <string name="str_title">Custom EditText</string>
            ~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:13: Warning: The resource R.string.str_phone_number appears to be unused [UnusedResources]
    <string name="str_phone_number">Enter your phone number</string>
            ~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:14: Warning: The resource R.string.str_credit_card appears to be unused [UnusedResources]
    <string name="str_credit_card">Enter your credit card number</string>
            ~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:15: Warning: The resource R.string.str_username appears to be unused [UnusedResources]
    <string name="str_username">Username</string>
            ~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:16: Warning: The resource R.string.str_password appears to be unused [UnusedResources]
    <string name="str_password">Password</string>
            ~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:17: Warning: The resource R.string.str_amount appears to be unused [UnusedResources]
    <string name="str_amount">Enter amount</string>
            ~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:18: Warning: The resource R.string.zero appears to be unused [UnusedResources]
    <string name="zero">0</string>
            ~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:19: Warning: The resource R.string.one appears to be unused [UnusedResources]
    <string name="one">1</string>
            ~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:20: Warning: The resource R.string.two appears to be unused [UnusedResources]
    <string name="two">2</string>
            ~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:21: Warning: The resource R.string.three appears to be unused [UnusedResources]
    <string name="three">3</string>
            ~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:22: Warning: The resource R.string.four appears to be unused [UnusedResources]
    <string name="four">4</string>
            ~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:23: Warning: The resource R.string.five appears to be unused [UnusedResources]
    <string name="five">5</string>
            ~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:24: Warning: The resource R.string.six appears to be unused [UnusedResources]
    <string name="six">6</string>
            ~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:25: Warning: The resource R.string.seven appears to be unused [UnusedResources]
    <string name="seven">7</string>
            ~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:26: Warning: The resource R.string.eight appears to be unused [UnusedResources]
    <string name="eight">8</string>
            ~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:27: Warning: The resource R.string.nine appears to be unused [UnusedResources]
    <string name="nine">9</string>
            ~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:28: Warning: The resource R.string.minus appears to be unused [UnusedResources]
    <string name="minus">-</string>
            ~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:29: Warning: The resource R.string.comma appears to be unused [UnusedResources]
    <string name="comma">,</string>
            ~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:30: Warning: The resource R.string.dot appears to be unused [UnusedResources]
    <string name="dot">.</string>
            ~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:31: Warning: The resource R.string.ok appears to be unused [UnusedResources]
    <string name="ok">OK</string>
            ~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml:4: Warning: The resource R.style.keyNoBg appears to be unused [UnusedResources]
    <style name="keyNoBg">
           ~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml:16: Warning: The resource R.style.key appears to be unused [UnusedResources]
    <style name="key" parent="keyNoBg">
           ~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml:20: Warning: The resource R.style.keyContainer appears to be unused [UnusedResources]
    <style name="keyContainer">
           ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.

   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests=true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests=true.

/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml:5: Warning: The standard Android way to capitalize Ok is "OK" (tip: use @android:string/ok instead) [ButtonCase]
    <string name="dialog_btn_ok">Ok</string>
                                 ~~

   Explanation for issues of type "ButtonCase":
   The standard capitalization for OK/Cancel dialogs is "OK" and "Cancel". To
   ensure that your dialogs use the standard strings, you can use the resource
   strings @android:string/ok and @android:string/cancel.

/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/CustomEditText.java:269: Warning: onTouch should call View#performClick when a click is detected [ClickableViewAccessibility]
            public boolean onTouch(View view, MotionEvent event) {
                           ~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java:239: Warning: Custom view `AppCompatEditText` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        editText.setOnTouchListener(new OnTouchListener() {
        ^
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java:241: Warning: onTouch should call View#performClick when a click is detected [ClickableViewAccessibility]
            public boolean onTouch(View v, MotionEvent event) {
                           ~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java:481: Warning: Custom view `AppCompatEditText` has setOnTouchListener called on it but does not override performClick [ClickableViewAccessibility]
        editText.setOnTouchListener(listener);
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java:578: Warning: Custom view MultiLineEditText overrides onTouchEvent but not performClick [ClickableViewAccessibility]
    public boolean onTouchEvent(MotionEvent event) {
                   ~~~~~~~~~~~~

   Explanation for issues of type "ClickableViewAccessibility":
   If a View that overrides onTouchEvent or uses an OnTouchListener does not
   also implement performClick and call it when clicks are detected, the View
   may not handle accessibility actions properly. Logic handling the click
   actions should ideally be placed in View#performClick as some accessibility
   services invoke performClick when a click action should occur.

0 errors, 60 warnings
