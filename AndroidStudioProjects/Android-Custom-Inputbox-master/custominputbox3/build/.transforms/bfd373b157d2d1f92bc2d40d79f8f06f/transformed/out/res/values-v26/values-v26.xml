<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="key" parent="keyNoBg">
        <item name="android:background">@drawable/key_bg</item>
    </style>
    <style name="keyContainer">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">0dp</item>
    </style>
    <style name="keyNoBg">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:layout_gravity">center</item>
        <item name="android:gravity">center</item>
        <item name="android:clickable">true</item>


        <item name="android:includeFontPadding">false</item>
        <item name="android:lineSpacingExtra">0dp</item>
        <item name="android:textSize">25sp</item>
        <item name="android:fontWeight">700</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>
</resources>