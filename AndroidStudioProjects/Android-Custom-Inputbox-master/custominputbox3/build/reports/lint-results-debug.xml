<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 7.2.2">

    <issue
        id="CustomViewStyleable"
        severity="Warning"
        message="By convention, the custom view (`MultiLineEditText`) and the declare-styleable (`CustomEditText`) should have the same name (various editor features rely on this convention)"
        category="Correctness"
        priority="6"
        summary="Mismatched Styleable/Custom View Name"
        explanation="The convention for custom views is to use a `declare-styleable` whose name matches the custom view class name. The IDE relies on this convention such that for example code completion can be offered for attributes in a custom view in layout XML resource files.&#xA;&#xA;(Similarly, layout parameter classes should use the suffix `_Layout`.)"
        errorLine1="        android.content.res.TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.CustomEditText);"
        errorLine2="                                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java"
            line="100"
            column="82"/>
    </issue>

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdkVersion 33"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle"
            line="9"
            column="9"/>
    </issue>

    <issue
        id="AppBundleLocaleChanges"
        severity="Warning"
        message="Found dynamic locale changes, but did not find corresponding Play Core library calls for downloading languages and splitting by language is not disabled in the `bundle` configuration"
        category="Correctness"
        priority="5"
        summary="App Bundle handling of runtime locale changes"
        explanation="When changing locales at runtime (e.g. to provide an in-app language switcher), the Android App Bundle must be configured to not split by locale or the Play Core library must be used to download additional locales at runtime."
        url="https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes"
        urls="https://developer.android.com/guide/app-bundle/configure-base#handling_language_changes"
        errorLine1="        configuration.locale = locale;"
        errorLine2="                      ~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/utils/LocaleHelper.java"
            line="75"
            column="23"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.appcompat:appcompat than 1.6.1 is available: 1.7.1"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.appcompat:appcompat:1.6.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle"
            line="27"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.core:core than 1.10.1 is available: 1.16.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    implementation &apos;androidx.core:core:1.10.1&apos;"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle"
            line="28"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.ext:junit than 1.1.5 is available: 1.3.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;androidx.test.ext:junit:1.1.5&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle"
            line="31"
            column="31"/>
    </issue>

    <issue
        id="GradleDependency"
        severity="Warning"
        message="A newer version of androidx.test.espresso:espresso-core than 3.5.1 is available: 3.7.0"
        category="Correctness"
        priority="4"
        summary="Obsolete Gradle Dependency"
        explanation="This detector looks for usages of libraries where the version you are using is not the current stable release. Using older versions is fine, and there are cases where you deliberately want to stick with an older version. However, you may simply not be aware that a more recent version is available, and that is what this lint check helps find."
        errorLine1="    androidTestImplementation &apos;androidx.test.espresso:espresso-core:3.5.1&apos;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/build.gradle"
            line="32"
            column="31"/>
    </issue>

    <issue
        id="DiscouragedApi"
        severity="Warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`)."
        category="Correctness"
        priority="2"
        summary="Using discouraged APIs"
        explanation="Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior)."
        errorLine1="            colorAttr = getContext().getResources().getIdentifier(&quot;colorAccent&quot;, &quot;attr&quot;, getContext().getPackageName());"
        errorLine2="                                                    ~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/CustomEditText.java"
            line="513"
            column="53"/>
    </issue>

    <issue
        id="Typos"
        severity="Warning"
        message="&quot;Ok&quot; is usually capitalized as &quot;OK&quot;"
        category="Correctness:Messages"
        priority="7"
        summary="Spelling error"
        explanation="This check looks through the string definitions, and if it finds any words that look like likely misspellings, they are flagged."
        errorLine1="    &lt;string name=&quot;dialog_btn_ok&quot;>Ok&lt;/string>"
        errorLine2="                                 ^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="5"
            column="34"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 16"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/CustomEditText.java"
            line="355"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_backspace` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_backspace.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_backspace_normal` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_backspace_normal.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_backspace_pressed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_backspace_pressed.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_check_circle` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_check_circle.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_check_circle_normal` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;vector android:height=&quot;40dp&quot; android:tint=&quot;#3DB6AC&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_check_circle_normal.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_check_circle_pressed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;vector android:height=&quot;40dp&quot; android:tint=&quot;#3DB6AC&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_check_circle_pressed.xml"
            line="1"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_comma` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_comma.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_comma_normal` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_comma_normal.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_comma_pressed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_comma_pressed.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_fingerprint` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_fingerprint.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_fingerprint_normal` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_fingerprint_normal.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.ic_fingerprint_pressed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/ic_fingerprint_pressed.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.key_bg` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.key_bg_normal` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg_normal.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.key_bg_pressed` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg_pressed.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.key_bg_transparent` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;shape/>"
        errorLine2="~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_bg_transparent.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.key_text_color` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_text_color.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.key_text_color2` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="&lt;selector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/drawable/key_text_color2.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.app_name` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;app_name&quot;>CustomInputBox&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="2"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.robotoBold` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;robotoBold&quot;>Roboto-Bold.ttf&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="3"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.robotoRegular` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;robotoRegular&quot;>Roboto-Regular.ttf&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="4"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.str_title` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;str_title&quot;>Custom EditText&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="12"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.str_phone_number` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;str_phone_number&quot;>Enter your phone number&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.str_credit_card` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;str_credit_card&quot;>Enter your credit card number&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.str_username` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;str_username&quot;>Username&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="15"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.str_password` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;str_password&quot;>Password&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="16"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.str_amount` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;str_amount&quot;>Enter amount&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.zero` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;zero&quot;>0&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.one` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;one&quot;>1&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.two` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;two&quot;>2&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.three` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;three&quot;>3&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.four` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;four&quot;>4&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.five` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;five&quot;>5&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.six` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;six&quot;>6&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.seven` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;seven&quot;>7&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.eight` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;eight&quot;>8&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="26"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.nine` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;nine&quot;>9&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.minus` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;minus&quot;>-&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.comma` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;comma&quot;>,&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.dot` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;dot&quot;>.&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.string.ok` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;string name=&quot;ok&quot;>OK&lt;/string>"
        errorLine2="            ~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.keyNoBg` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;style name=&quot;keyNoBg&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.key` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;style name=&quot;key&quot; parent=&quot;keyNoBg&quot;>"
        errorLine2="           ~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml"
            line="16"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.style.keyContainer` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests=true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests=true."
        errorLine1="    &lt;style name=&quot;keyContainer&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/styles.xml"
            line="20"
            column="12"/>
    </issue>

    <issue
        id="ButtonCase"
        severity="Warning"
        message="The standard Android way to capitalize Ok is &quot;OK&quot; (tip: use `@android:string/ok` instead)"
        category="Usability"
        priority="2"
        summary="Cancel/OK dialog button capitalization"
        explanation="The standard capitalization for OK/Cancel dialogs is &quot;OK&quot; and &quot;Cancel&quot;. To ensure that your dialogs use the standard strings, you can use the resource strings @android:string/ok and @android:string/cancel."
        errorLine1="    &lt;string name=&quot;dialog_btn_ok&quot;>Ok&lt;/string>"
        errorLine2="                                 ~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/res/values/strings.xml"
            line="5"
            column="34"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="`onTouch` should call `View#performClick` when a click is detected"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="            public boolean onTouch(View view, MotionEvent event) {"
        errorLine2="                           ~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/CustomEditText.java"
            line="269"
            column="28"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="Custom view ``AppCompatEditText`` has `setOnTouchListener` called on it but does not override `performClick`"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="        editText.setOnTouchListener(new OnTouchListener() {"
        errorLine2="        ^">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java"
            line="239"
            column="9"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="`onTouch` should call `View#performClick` when a click is detected"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="            public boolean onTouch(View v, MotionEvent event) {"
        errorLine2="                           ~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java"
            line="241"
            column="28"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="Custom view ``AppCompatEditText`` has `setOnTouchListener` called on it but does not override `performClick`"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="        editText.setOnTouchListener(listener);"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java"
            line="481"
            column="9"/>
    </issue>

    <issue
        id="ClickableViewAccessibility"
        severity="Warning"
        message="Custom view `MultiLineEditText` overrides `onTouchEvent` but not `performClick`"
        category="Accessibility"
        priority="6"
        summary="Accessibility in Custom Views"
        explanation="If a `View` that overrides `onTouchEvent` or uses an `OnTouchListener` does not also implement `performClick` and call it when clicks are detected, the `View` may not handle accessibility actions properly. Logic handling the click actions should ideally be placed in `View#performClick` as some accessibility services invoke `performClick` when a click action should occur."
        errorLine1="    public boolean onTouchEvent(MotionEvent event) {"
        errorLine2="                   ~~~~~~~~~~~~">
        <location
            file="/Users/<USER>/AndroidStudioProjects/Android-Custom-Inputbox-master/custominputbox3/src/main/java/com/wkb/custominputbox3/MultiLineEditText.java"
            line="578"
            column="20"/>
    </issue>

</issues>
