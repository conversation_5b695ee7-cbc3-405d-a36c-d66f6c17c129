# CustomInputBox3 - Java Version

## 重要更新

**MultiLineEditText已经完全替代CustomEditText！** MultiLineEditText集成了CustomEditText的所有功能，并提供了更好的字数统计显示效果。

## 主要特性

  - 边框样式自定义
  - 密码显示/隐藏功能
  - 清除按钮功能
  - 前缀文本功能
  - 输入模式验证
  - 正则表达式验证
  - 自定义字体支持
  - 模式化输入（如电话号码、信用卡号等）
  - Drawable点击事件
  - **多行输入支持**
  - **字数统计显示**
  - **优化的触摸事件处理**

## 使用方法

### 1. 添加依赖

在app模块的build.gradle中添加：

```gradle
implementation project(path: ':custominputbox3')
```

### 2. 在布局中使用

```xml
<!-- 默认单行模式（推荐） -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:hint="输入文本"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#2196F3"
    app:edt_setCornerRadius="8dp" />

<!-- 启用字数统计的单行模式 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:hint="输入文本"
    app:edt_showCharCount="true"
    app:edt_maxCharCount="50"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#4CAF50"
    app:edt_setCornerRadius="8dp" />

<!-- 多行输入框 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="120dp"
    android:hint="请输入评论内容..."
    android:inputType="textMultiLine"
    app:edt_multiLine="true"
    app:edt_maxCharCount="60"
    app:edt_showCharCount="true"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#FF9800"
    app:edt_setCornerRadius="8dp" />
```

### 3. 在代码中使用

```java
// 推荐使用MultiLineEditText
MultiLineEditText editText = findViewById(R.id.editText);
editText.setDrawableClickListener(new DrawableClickListener() {
    @Override
    public void onRightClick() {
        // 处理右侧drawable点击事件
    }
});

// 多行输入框示例
MultiLineEditText multiLineEditText = findViewById(R.id.multiLineEditText);
multiLineEditText.setMultiLine(true);
multiLineEditText.setMaxCharCount(100);
multiLineEditText.setCharCountTextColor(Color.GRAY);
multiLineEditText.setShowCharCount(true); // 控制是否显示字数统计

// 设置前缀
multiLineEditText.setPrefix("$");

// 设置清除按钮
multiLineEditText.setClearIconVisible(true);
```

## 可用属性

| 属性 | 类型 | 描述 |
|------|------|------|
| `edt_setBorderView` | boolean | 是否显示边框 |
| `edt_setBorderColor` | color | 边框颜色 |
| `edt_setBackgroundColor` | color | 背景颜色 |
| `edt_setCornerRadius` | dimension | 圆角半径 |
| `edt_setStrokeWidth` | dimension | 边框宽度 |
| `edt_setClearIconVisible` | boolean | 是否显示清除按钮 |
| `edt_clearIconTint` | color | 清除按钮颜色 |
| `edt_hideShowPasswordIconTint` | color | 密码显示/隐藏按钮颜色 |
| `edt_setPrefix` | string | 前缀文本 |
| `edt_setPrefixTextColor` | color | 前缀文本颜色 |
| `edt_setFont` | string | 字体文件名 |
| `edt_minLength` | string | 最小长度验证 |
| `edt_regexp` | string | 正则表达式验证 |
| `edt_pattern` | string | 输入模式（如电话号码格式） |
| `edt_specialChar` | string | 模式中的特殊字符（默认#） |
| `edt_showPatternAsHint` | boolean | 是否将模式显示为提示 |
| `edt_cursor` | reference | 光标颜色 |
| `edt_multiLine` | boolean | 是否启用多行输入模式 |
| `edt_maxCharCount` | integer | 最大字符数限制 |
| `edt_charCountTextColor` | color | 字数统计文本颜色 |
| `edt_charCountTextSize` | dimension | 字数统计文本大小 |
| `edt_showCharCount` | boolean | 是否显示字数统计（默认false） |

## 示例

查看 `app/src/main/java/com/wkb/custominputbox/ExampleActivity3.java` 和对应的布局文件来了解完整的使用示例。


## 多行输入功能

### 新的MultiLineEditText控件

为了解决字数统计被覆盖的问题，我们创建了一个全新的`MultiLineEditText`控件，它使用FrameLayout布局来实现更好的字数统计显示效果。

#### 特性
- **默认单行模式**：默认不启用多行输入
- **默认不显示字数统计**：需要时可通过属性或代码启用
- **无系统默认横线**：去掉了EditText默认的底部横线，但保留hint显示功能
- **多行模式**：字数统计独立显示在右下角，不随文本滚动
- **单行模式**：文本和数字保持左右结构，数字在右侧，文本不会覆盖数字
- **智能布局**：单行模式下以右侧控件宽度为准，剩余空间给输入框
- 自动设置最小3行，最大5行（多行模式）
- 字数统计透明显示，无背景干扰
- 超过字数限制时，统计文字变为红色，并限制输入
- 支持自定义字数限制、文字颜色和大小
- 支持控制字数统计的显示/隐藏
- 字数统计TextView不接收触摸事件，确保EditText可以正常获取焦点
- 重写了触摸事件处理，确保单行模式下EditText可以正常接收输入

### 多行输入框示例

```xml
<!-- 多行模式：字数统计在右下角 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="120dp"
    android:hint="请输入评论内容..."
    android:inputType="textMultiLine"
    app:edt_multiLine="true"
    app:edt_maxCharCount="60"
    app:edt_charCountTextColor="#666666"
    app:edt_charCountTextSize="12sp"
    app:edt_showCharCount="true"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#FF9800"
    app:edt_setCornerRadius="8dp" />

<!-- 单行模式：字数统计在右侧 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:hint="请输入简介..."
    android:inputType="text"
    app:edt_multiLine="false"
    app:edt_maxCharCount="100"
    app:edt_charCountTextColor="#2196F3"
    app:edt_charCountTextSize="14sp"
    app:edt_showCharCount="true"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#4CAF50"
    app:edt_setCornerRadius="8dp" />

<!-- 隐藏字数统计 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:hint="字数统计已隐藏..."
    android:inputType="textMultiLine"
    app:edt_multiLine="true"
    app:edt_maxCharCount="50"
    app:edt_showCharCount="false"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#9C27B0"
    app:edt_setCornerRadius="8dp" />
```

## 注意事项

- 确保在assets/fonts目录下放置自定义字体文件
- 密码字段会自动启用显示/隐藏功能
- 清除按钮只在有文本时显示
- 模式化输入会自动格式化用户输入
- 多行输入框会自动设置最大字符数限制
- 字数统计透明显示，无背景干扰
- 可以通过`edt_showCharCount`属性控制字数统计的显示/隐藏
- **推荐使用MultiLineEditText**：新的MultiLineEditText控件解决了字数统计被覆盖的问题
- **多行模式**：字数统计独立显示在右下角，不随文本滚动
- **单行模式**：文本和数字保持左右结构，数字在右侧，文本不会覆盖数字
- **焦点问题已修复**：字数统计TextView不接收触摸事件，确保EditText可以正常获取焦点和输入
- **触摸事件优化**：重写了dispatchTouchEvent方法，确保单行模式下触摸事件正确传递给EditText 