package com.wkb.custominputbox3.utils;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.res.Resources;
import android.os.Build;

import com.wkb.custominputbox3.R;

import java.text.NumberFormat;
import java.util.Locale;

public class CommonUtils {

    /**
     * Show validation Dialog.
     */
    public static void showAlertDialog(Context context, String title, String message) {
        ((Activity) context).runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.Builder mAlertDialog = new AlertDialog.Builder(context);
                AlertDialog mDialog = mAlertDialog.create();
                mDialog.setCanceledOnTouchOutside(false);
                mDialog.setTitle(title);
                if (message != null && !"".equals(message)) {
                    mDialog.setMessage(message);
                } else {
                    mDialog.setMessage(context.getString(R.string.something_wrong));
                }

                mDialog.setButton(AlertDialog.BUTTON_NEUTRAL, context.getResources().getString(R.string.dialog_btn_ok), (dialog, which) -> dialog.dismiss());
                mDialog.show();
            }
        });
    }

    /**
     * setCurrencyFormat method is use for convert currency input to respected country format.
     */
    public static String setCurrencyFormat(Context context, String amount) {
        String deviceLang = Resources.getSystem().getConfiguration().locale.getLanguage();

        String locale;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            locale = context.getResources().getConfiguration().getLocales().get(0).getCountry();
        } else {
            locale = context.getResources().getConfiguration().locale.getCountry();
        }

        Locale currentLocale = new Locale(deviceLang, locale);
        NumberFormat currencyFormatter = NumberFormat.getCurrencyInstance(currentLocale);

        return currencyFormatter.format(Double.parseDouble(amount));
    }
} 