package com.wkb.custominputbox3;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.text.Editable;
import android.text.TextUtils;
import android.text.method.DigitsKeyListener;
import android.text.method.PasswordTransformationMethod;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import static android.view.View.MeasureSpec;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;

import com.wkb.custominputbox3.utils.CommonUtils;
import com.wkb.custominputbox3.utils.DrawableClickListener;

import java.util.Objects;

public class MultiLineEditText extends FrameLayout {
    private static final int DEFAULTCOLOR = Color.parseColor("#808080");
    private static final int TYPE_TEXT_VARIATION_PASSWORD = 129;
    private static final int TYPE_NUMBER_VARIATION_PASSWORD = 18;
    private static final int DEFAULT_PADDING = 15;

    private AppCompatEditText editText;
    private TextView charCountTextView;
    
    // 样式属性
    private int mBackgroundColor = Color.TRANSPARENT;
    private int mBorderColor = DEFAULTCOLOR;
    private float mCornerRadius = 0f;
    private float mStrokeWidth = 1f;
    private boolean isBorderView = false;
    
    // 多行输入相关属性
    private boolean isMultiLine = false;
    private int maxCharCount = 60;
    private int charCountTextColor = Color.GRAY;
    private float charCountTextSize = 12f;
    private boolean showCharCount = false; // 默认不显示字数统计
    
    // CustomEditText功能属性
    private boolean isClearIconVisible = false;
    private boolean isPassword = false;
    private boolean isShowingPassword = false;
    private Drawable imgCloseButton = null;
    private Drawable drawableEnd = null;
    private int cursorDrawable = 0;
    private String minLength = null;
    private String regexp = null;
    private String inputtext = null;
    private String font = null;
    private String mPrefix = null;
    private int prefixTextColor = 0;
    private DrawableClickListener clickListener = null;
    
    // 清除按钮相关
    private Drawable clearIcon = null;
    private int clearIconTint = DEFAULTCOLOR;
    private int hideShowIconTint = DEFAULTCOLOR;
    
    // 前缀绘制相关
    private Paint prefixPaint;
    private Rect prefixBounds;
    private float prefixWidth = 0f;

    // 动态右侧空间
    private int dynamicRightMargin = 0;

    public MultiLineEditText(Context context) {
        super(context);
        init(context, null);
    }

    public MultiLineEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public MultiLineEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    private void init(Context context, AttributeSet attrs) {
        // 读取属性
        android.content.res.TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.CustomEditText);
        
        // 基础样式属性
        isBorderView = a.getBoolean(R.styleable.CustomEditText_edt_setBorderView, false);
        mBorderColor = a.getColor(R.styleable.CustomEditText_edt_setBorderColor, DEFAULTCOLOR);
        mBackgroundColor = a.getColor(R.styleable.CustomEditText_edt_setBackgroundColor, Color.TRANSPARENT);
        mCornerRadius = a.getDimension(R.styleable.CustomEditText_edt_setCornerRadius, 0f);
        mStrokeWidth = a.getDimension(R.styleable.CustomEditText_edt_setStrokeWidth, 1f);
        
        // 多行输入相关属性
        isMultiLine = a.getBoolean(R.styleable.CustomEditText_edt_multiLine, false);
        maxCharCount = a.getInteger(R.styleable.CustomEditText_edt_maxCharCount, 60);
        charCountTextColor = a.getColor(R.styleable.CustomEditText_edt_charCountTextColor, Color.GRAY);
        charCountTextSize = a.getDimension(R.styleable.CustomEditText_edt_charCountTextSize, 12f);
        showCharCount = a.getBoolean(R.styleable.CustomEditText_edt_showCharCount, false); // 默认不显示字数统计
        
        // CustomEditText功能属性
        isClearIconVisible = a.getBoolean(R.styleable.CustomEditText_edt_setClearIconVisible, false);
        cursorDrawable = a.getResourceId(R.styleable.CustomEditText_edt_cursor, 0);
        this.font = a.getString(R.styleable.CustomEditText_edt_setFont);
        mPrefix = a.getString(R.styleable.CustomEditText_edt_setPrefix);
        minLength = a.getString(R.styleable.CustomEditText_edt_minLength);
        regexp = a.getString(R.styleable.CustomEditText_edt_regexp);
        prefixTextColor = a.getColor(R.styleable.CustomEditText_edt_setPrefixTextColor, 0);
        clearIconTint = a.getColor(R.styleable.CustomEditText_edt_clearIconTint, DEFAULTCOLOR);
        hideShowIconTint = a.getColor(R.styleable.CustomEditText_edt_hideShowPasswordIconTint, DEFAULTCOLOR);
        
        a.recycle();

        // 创建EditText
        createEditText(context);
        
        // 创建字数统计TextView
        if (showCharCount) {
            createCharCountTextView(context);
        }
        
        // 设置背景
        if (isBorderView) {
            setBackground(createBackgroundDrawable());
        }
        
        // 设置布局参数
        setupLayout();
        
        // 初始化前缀绘制
        initPrefixDrawing();
    }

    private void createEditText(Context context) {
        editText = new AppCompatEditText(context) {
            @Override
            protected void onDraw(Canvas canvas) {
                super.onDraw(canvas);
                // 绘制前缀
                if (mPrefix != null && mPrefix.length() > 0) {
                    drawPrefix(canvas);
                }
            }
        };

        // 去掉系统默认的底部横线，但保留hint显示
        // 设置null背景来移除默认样式，同时保持hint显示
        editText.setBackground(null);
        
        // 设置基本属性
        if (isMultiLine) {
            editText.setMaxLines(5);
            editText.setMinLines(3);
            editText.setGravity(Gravity.TOP | Gravity.START);
            editText.setFilters(new android.text.InputFilter[]{new android.text.InputFilter.LengthFilter(maxCharCount)});
        } else {
            editText.setSingleLine(true);
        }
        
        // 设置字体
        if (font != null) {
            try {
                editText.setTypeface(Typefaces.get(context, font));
            } catch (Exception ignored) {
            }
        }
        

        
        // 设置清除按钮
        if (isClearIconVisible) {
            setupClearButton();
        }
        
        // 设置文本变化监听
        editText.addTextChangedListener(new android.text.TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}

            @Override
            public void afterTextChanged(Editable s) {
                // 限制输入长度
                if (s.length() > maxCharCount) {
                    s.delete(maxCharCount, s.length());
                }
                updateCharCount();
                handleClearButton();
            }
        });
        
        // 设置焦点变化监听
        editText.setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (minLength != null) {
                    if (!hasFocus) {
                        if (editText.getText().toString().trim().length() > 0) {
                            if (editText.getText().toString().trim().length() < Integer.parseInt(minLength)) {
                                CommonUtils.showAlertDialog(context, "Alert", context.getString(R.string.error_min_value, minLength));
                            }
                        }
                    }
                }
                if (regexp != null) {
                    if (!hasFocus) {
                        inputtext = editText.getEditableText().toString().trim();
                        if (editText.getText().toString().trim().length() > 0) {
                            if (!inputtext.matches(regexp)) {
                                CommonUtils.showAlertDialog(context, "Alert", context.getString(R.string.error_regex));
                            }
                        }
                    }
                }
                handleClearButton();
            }
        });
        
        // 设置触摸监听
        editText.setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                if (isClearIconVisible && event.getAction() == MotionEvent.ACTION_UP && editText.getCompoundDrawables()[2] != null) {
                    if (editText.getCompoundDrawables()[2].getBounds() != null &&
                            event.getRawX() >= (editText.getRight() - editText.getCompoundDrawables()[2].getBounds().width())) {
                        editText.setText("");
                        return true;
                    }
                }
                return false;
            }
        });
    }

    private void createCharCountTextView(Context context) {
        charCountTextView = new TextView(context);
        charCountTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, charCountTextSize);
        charCountTextView.setTextColor(charCountTextColor);
        charCountTextView.setGravity(Gravity.END | Gravity.CENTER_VERTICAL);
        charCountTextView.setPadding(10, 5, 10, 5);

        // 移除白色背景
        // GradientDrawable bg = new GradientDrawable();
        // bg.setColor(Color.WHITE);
        // bg.setAlpha(200);
        // bg.setCornerRadius(4f);
        // charCountTextView.setBackground(bg);

        updateCharCount();
        calculateDynamicRightMargin();
    }

    private void setupLayout() {
        // 清除所有子视图
        removeAllViews();
        
        if (isMultiLine && showCharCount) {
            // 多行模式：EditText在上，字数统计在下
            LayoutParams editTextParams = new LayoutParams(
                    LayoutParams.MATCH_PARENT,
                    LayoutParams.MATCH_PARENT
            );
            editTextParams.bottomMargin = 30; // 为字数统计留出空间
            addView(editText, editTextParams);
            
            LayoutParams charCountParams = new LayoutParams(
                    LayoutParams.WRAP_CONTENT,
                    LayoutParams.WRAP_CONTENT
            );
            charCountParams.gravity = Gravity.BOTTOM | Gravity.END;
            charCountParams.bottomMargin = 5;
            charCountParams.rightMargin = 5;
            // 设置字数统计TextView不接收触摸事件
            charCountTextView.setClickable(false);
            charCountTextView.setFocusable(false);
            charCountTextView.setFocusableInTouchMode(false);
            // 设置TextView不阻挡触摸事件
            charCountTextView.setEnabled(false);
            // 设置TextView在Z轴上的顺序，确保不阻挡EditText
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                charCountTextView.setElevation(0);
            }
            addView(charCountTextView, charCountParams);
            
        } else if (!isMultiLine && showCharCount) {
            // 单行模式：EditText在左，字数统计在右
            // 先计算动态右侧空间
            calculateDynamicRightMargin();

            // 先添加EditText，让它占据大部分空间
            LayoutParams editTextParams = new LayoutParams(
                    LayoutParams.MATCH_PARENT,
                    LayoutParams.MATCH_PARENT
            );
            editTextParams.rightMargin = dynamicRightMargin > 0 ? dynamicRightMargin : 80; // 使用动态计算的空间，如果为0则使用默认值
            addView(editText, editTextParams);
            
            LayoutParams charCountParams = new LayoutParams(
                    LayoutParams.WRAP_CONTENT,
                    LayoutParams.MATCH_PARENT
            );
            charCountParams.gravity = Gravity.END | Gravity.CENTER_VERTICAL;
            charCountParams.rightMargin = 0;
            // 设置字数统计TextView不接收触摸事件
            charCountTextView.setClickable(false);
            charCountTextView.setFocusable(false);
            charCountTextView.setFocusableInTouchMode(false);
            // 设置TextView不阻挡触摸事件
            charCountTextView.setEnabled(false);
            // 设置TextView在Z轴上的顺序，确保不阻挡EditText
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                charCountTextView.setElevation(0);
            }
            addView(charCountTextView, charCountParams);
//
        } else {
            // 不显示字数统计
            LayoutParams editTextParams = new LayoutParams(
                    LayoutParams.MATCH_PARENT,
                    LayoutParams.MATCH_PARENT
            );
            addView(editText, editTextParams);
        }
    }

    private void updateCharCount() {
        if (charCountTextView != null) {
            int currentLength = editText.getText().length();
            String charCountText = currentLength + "/" + maxCharCount;
            charCountTextView.setText(charCountText);

            // 如果字数超过限制，改变颜色
            if (currentLength > maxCharCount) {
                charCountTextView.setTextColor(Color.RED);
            } else {
                charCountTextView.setTextColor(charCountTextColor);
            }

            // 重新计算动态右侧空间
            calculateDynamicRightMargin();
        }
    }

    private void calculateDynamicRightMargin() {
        if (charCountTextView != null && !isMultiLine && showCharCount) {
            // 测量字数统计TextView的实际宽度
            charCountTextView.measure(
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
            );
            int textViewWidth = charCountTextView.getMeasuredWidth();

            // 添加一些额外的边距（左右各10dp的padding + 10dp的右边距）
            int extraMargin = (int) TypedValue.applyDimension(
                TypedValue.COMPLEX_UNIT_DIP, 30,
                getContext().getResources().getDisplayMetrics()
            );

            dynamicRightMargin = textViewWidth + extraMargin;

            // 如果布局已经设置，重新调整EditText的右边距
            if (editText.getParent() != null) {
                LayoutParams editTextParams = (LayoutParams) editText.getLayoutParams();
                if (editTextParams != null) {
                    editTextParams.rightMargin = dynamicRightMargin;
                    editText.setLayoutParams(editTextParams);
                }
            }
        }
    }

    private Drawable createBackgroundDrawable() {
        GradientDrawable shape = new GradientDrawable();
        shape.setShape(GradientDrawable.RECTANGLE);
        shape.setCornerRadius(mCornerRadius);
        shape.setColor(mBackgroundColor);
        shape.setStroke((int) mStrokeWidth, mBorderColor);
        return shape;
    }

    // 初始化前缀绘制
    private void initPrefixDrawing() {
        if (mPrefix != null && mPrefix.length() > 0) {
            prefixPaint = new Paint();
            prefixPaint.setAntiAlias(true);
            prefixPaint.setTextSize(editText.getTextSize());
            if (prefixTextColor != 0) {
                prefixPaint.setColor(prefixTextColor);
            } else {
                prefixPaint.setColor(editText.getCurrentTextColor());
            }
            
            prefixBounds = new Rect();
            prefixPaint.getTextBounds(mPrefix, 0, mPrefix.length(), prefixBounds);
            prefixWidth = prefixPaint.measureText(mPrefix);
            
            // 设置左边距为前缀宽度
            editText.setPadding((int) prefixWidth + editText.getPaddingLeft(), 
                               editText.getPaddingTop(), 
                               editText.getPaddingRight(), 
                               editText.getPaddingBottom());
        }
    }

    // 绘制前缀
    private void drawPrefix(Canvas canvas) {
        if (prefixPaint != null && mPrefix != null) {
            float x = editText.getPaddingLeft() - prefixWidth;
            float y = editText.getBaseline();
            canvas.drawText(mPrefix, x, y, prefixPaint);
        }
    }

    // 设置清除按钮
    private void setupClearButton() {
        if (isClearIconVisible) {
            clearIcon = ContextCompat.getDrawable(getContext(), android.R.drawable.ic_menu_close_clear_cancel);
            if (clearIcon != null) {
                clearIcon = DrawableCompat.wrap(clearIcon);
                DrawableCompat.setTint(clearIcon, clearIconTint);
                clearIcon.setBounds(0, 0, clearIcon.getIntrinsicWidth(), clearIcon.getIntrinsicHeight());
            }
        }
    }

    // 处理清除按钮显示/隐藏
    private void handleClearButton() {
        if (isClearIconVisible && clearIcon != null) {
            if (editText.hasFocus() && !TextUtils.isEmpty(editText.getText())) {
                editText.setCompoundDrawables(editText.getCompoundDrawables()[0], 
                                            editText.getCompoundDrawables()[1], 
                                            clearIcon, 
                                            editText.getCompoundDrawables()[3]);
            } else {
                editText.setCompoundDrawables(editText.getCompoundDrawables()[0], 
                                            editText.getCompoundDrawables()[1], 
                                            null, 
                                            editText.getCompoundDrawables()[3]);
            }
        }
    }

    // 代理方法，将调用转发给内部的EditText
    public String getText() {
        return editText.getText().toString();
    }

    public void setText(String text) {
        editText.setText(text);
    }

    public Editable getEditableText() {
        return editText.getEditableText();
    }

    public void setHint(String hint) {
        editText.setHint(hint);
    }

    public void setInputType(int type) {
        editText.setInputType(type);
    }

    public void setMaxLines(int maxLines) {
        editText.setMaxLines(maxLines);
    }

    public void setMinLines(int minLines) {
        editText.setMinLines(minLines);
    }

    public void setGravity(int gravity) {
        editText.setGravity(gravity);
    }

    public void setTextSize(float size) {
        editText.setTextSize(size);
    }

    public void setTextColor(int color) {
        editText.setTextColor(color);
    }

    public void setPadding(int left, int top, int right, int bottom) {
        editText.setPadding(left, top, right, bottom);
    }

    public void addTextChangedListener(android.text.TextWatcher watcher) {
        editText.addTextChangedListener(watcher);
    }

    public void setOnFocusChangeListener(OnFocusChangeListener listener) {
        editText.setOnFocusChangeListener(listener);
    }

    public void setOnTouchListener(OnTouchListener listener) {
        editText.setOnTouchListener(listener);
    }

    public void setDrawableClickListener(DrawableClickListener listener) {
        this.clickListener = listener;
    }

    // 多行输入相关方法
    public boolean isMultiLine() {
        return isMultiLine;
    }

    public void setMultiLine(boolean multiLine) {
        this.isMultiLine = multiLine;
        if (multiLine) {
            editText.setMaxLines(5);
            editText.setMinLines(3);
            editText.setGravity(Gravity.TOP | Gravity.START);
            editText.setFilters(new android.text.InputFilter[]{new android.text.InputFilter.LengthFilter(maxCharCount)});
        }
        setupLayout();
    }

    public int getMaxCharCount() {
        return maxCharCount;
    }

    public void setMaxCharCount(int maxCharCount) {
        this.maxCharCount = maxCharCount;
        editText.setFilters(new android.text.InputFilter[]{new android.text.InputFilter.LengthFilter(maxCharCount)});
        updateCharCount();
    }

    public int getCharCountTextColor() {
        return charCountTextColor;
    }

    public void setCharCountTextColor(int charCountTextColor) {
        this.charCountTextColor = charCountTextColor;
        if (charCountTextView != null) {
            charCountTextView.setTextColor(charCountTextColor);
        }
    }

    public float getCharCountTextSize() {
        return charCountTextSize;
    }

    public void setCharCountTextSize(float charCountTextSize) {
        this.charCountTextSize = charCountTextSize;
        if (charCountTextView != null) {
            charCountTextView.setTextSize(TypedValue.COMPLEX_UNIT_PX, charCountTextSize);
        }
    }

    public boolean isShowCharCount() {
        return showCharCount;
    }

    public void setShowCharCount(boolean showCharCount) {
        this.showCharCount = showCharCount;
        if (showCharCount && charCountTextView == null) {
            createCharCountTextView(getContext());
        }
        setupLayout();
    }

    // CustomEditText功能方法
    public void setPrefix(String prefix) {
        this.mPrefix = prefix;
        initPrefixDrawing();
        editText.invalidate();
    }

    public String getPrefix() {
        return mPrefix;
    }

    public void setClearIconVisible(boolean visible) {
        this.isClearIconVisible = visible;
        if (visible) {
            setupClearButton();
        }
        handleClearButton();
    }

    public boolean isClearIconVisible() {
        return isClearIconVisible;
    }

    // 获取内部EditText的引用
    public AppCompatEditText getEditText() {
        return editText;
    }
    
    // 重写触摸事件处理，确保EditText可以接收触摸事件
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        // 将触摸事件传递给EditText
        return editText.onTouchEvent(event) || super.onTouchEvent(event);
    }
    
    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        // 不拦截触摸事件，让子视图处理
        return false;
    }
    
    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        // 确保触摸事件正确传递给EditText
        if (editText != null) {
            // 检查触摸事件是否在EditText的区域内
            float x = ev.getX();
            float y = ev.getY();
            
            // 如果是单行模式且有字数统计，检查是否在EditText区域内
            if (!isMultiLine && showCharCount) {
                // 计算EditText的实际区域（排除右侧字数统计区域）
                int editTextRight = getWidth() - (dynamicRightMargin > 0 ? dynamicRightMargin : 80); // 使用动态计算的空间
                if (x <= editTextRight) {
                    // 在EditText区域内，直接传递给EditText
                    return editText.dispatchTouchEvent(ev);
                }
            } else {
                // 多行模式或没有字数统计，直接传递给EditText
                return editText.dispatchTouchEvent(ev);
            }
        }
        return super.dispatchTouchEvent(ev);
    }
} 