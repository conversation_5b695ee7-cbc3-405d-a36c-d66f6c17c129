package com.wkb.custominputbox3.utils;

import android.content.Context;
import android.preference.PreferenceManager;

import java.util.Locale;

public class LocaleHelper {

    private static final String SELECTED_LANGUAGE = "Locale.Helper.Selected.Language";
    private Context context;

    public static class Factory {
        public static LocaleHelper onCreate() {
            return new LocaleHelper();
        }

        public static LocaleHelper getLanguage() {
            return new LocaleHelper();
        }
    }

    public void onCreate(Context context) {
        this.context = context;
        String lang;
        if (getLanguage(context) == null || getLanguage(context).isEmpty()) {
            lang = getPersistedData(context, Locale.getDefault().getLanguage());
        } else {
            lang = getLanguage(context);
        }

        setLocale(context, lang);
        onAttach(context);
    }

    public void onCreate(Context context, String defaultLanguage) {
        String lang = getPersistedData(context, defaultLanguage);
        setLocale(context, lang);
    }

    public String getLanguage(Context context) {
        return getPersistedData(context, Locale.getDefault().getLanguage());
    }

    public Context setLocale(Context context, String language) {
        persist(context, language);
        return updateResources(context, language);
    }

    public Context onAttach(Context context) {
        String locale = getPersistedData(context, getLanguage(context));
        return setLocale(context, locale);
    }

    private String getPersistedData(Context context, String defaultLanguage) {
        android.content.SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        return preferences.getString(SELECTED_LANGUAGE, defaultLanguage);
    }

    private void persist(Context context, String language) {
        android.content.SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        android.content.SharedPreferences.Editor editor = preferences.edit();

        editor.putString(SELECTED_LANGUAGE, language);
        editor.apply();
    }

    private Context updateResources(Context context, String language) {
        Locale locale = new Locale(language);
        Locale.setDefault(locale);

        android.content.res.Resources resources = context.getResources();

        android.content.res.Configuration configuration = resources.getConfiguration();
        configuration.locale = locale;

        resources.updateConfiguration(configuration, resources.getDisplayMetrics());
        return context;
    }
} 