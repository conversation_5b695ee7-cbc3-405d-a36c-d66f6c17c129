package com.wkb.custominputbox3;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.text.Editable;
import android.text.TextUtils;
import android.text.method.DigitsKeyListener;
import android.text.method.PasswordTransformationMethod;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.MotionEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.TextView;

import androidx.annotation.ColorInt;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.drawable.DrawableCompat;

import com.wkb.custominputbox3.utils.CommonUtils;
import com.wkb.custominputbox3.utils.DrawableClickListener;

import java.util.Objects;

public class CustomEditText extends AppCompatEditText {
    private static final int DEFAULTCOLOR = Color.parseColor("#808080");
    private static final int TYPE_TEXT_VARIATION_PASSWORD = 129;
    private static final int TYPE_NUMBER_VARIATION_PASSWORD = 18;
    private static final int DEFAULT_PADDING = 15;

    private int mBackgroundColor = 0;
    private int clearIconTint = 0;
    private int hideShowIconTint = 0;
    private int prefixTextColor = 0;
    private int cPadding = 0;
    private int cPaddingLeft = 0;
    private int cPaddingTop = 0;
    private int cPaddingRight = 0;
    private int cPaddingBottom = 0;
    private float mCornerRadius = 0f;
    private float mStrokeWidth = 1f;
    private float mOriginalLeftPadding = -1f;
    private boolean isClearIconVisible = false;
    private boolean isPassword = false;
    private boolean isShowingPassword = false;
    private Drawable imgCloseButton = null;
    private Drawable drawableEnd = null;
    private int cursorDrawable = 0;
    private String minLength = null;
    private String regexp = null;
    private String inputtext = null;
    private String font = null;
    private String mPrefix = null;
    private DrawableClickListener clickListener = null;
    
    // 多行输入相关属性
    private boolean isMultiLine = false;
    private int maxCharCount = 60;
    private int charCountTextColor = Color.GRAY;
    private float charCountTextSize = 12f;
    private Paint charCountPaint;
    private boolean showCharCount = true; // 新增：控制是否显示字数统计
    
    public static final int DRAWABLE_LEFT = 0;
    public static final int DRAWABLE_TOP = 1;
    public static final int DRAWABLE_RIGHT = 2;
    public static final int DRAWABLE_BOTTOM = 3;

    public CustomEditText(Context context) {
        super(context);
        init(context, null);
    }

    public CustomEditText(Context context, AttributeSet attrs) {
        super(context, attrs);
        init(context, attrs);
    }

    public CustomEditText(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context, attrs);
    }

    public String getPrefix() {
        return this.mPrefix;
    }

    public void setPrefix(String prefix) {
        this.mPrefix = prefix;
        calculatePrefix();
        invalidate();
    }

    public String getFont() {
        return font;
    }

    public void setFontName(String fontName) {
        this.font = fontName;
        setFont();
    }

    public boolean isMultiLine() {
        return isMultiLine;
    }

    public void setMultiLine(boolean multiLine) {
        this.isMultiLine = multiLine;
        if (multiLine) {
            setMaxLines(5);
            setMinLines(3);
            setGravity(android.view.Gravity.TOP | android.view.Gravity.START);
        }
        invalidate();
    }

    public int getMaxCharCount() {
        return maxCharCount;
    }

    public void setMaxCharCount(int maxCharCount) {
        this.maxCharCount = maxCharCount;
        invalidate();
    }

    public int getCharCountTextColor() {
        return charCountTextColor;
    }

    public void setCharCountTextColor(int charCountTextColor) {
        this.charCountTextColor = charCountTextColor;
        if (charCountPaint != null) {
            charCountPaint.setColor(charCountTextColor);
        }
        invalidate();
    }

    public float getCharCountTextSize() {
        return charCountTextSize;
    }

    public void setCharCountTextSize(float charCountTextSize) {
        this.charCountTextSize = charCountTextSize;
        if (charCountPaint != null) {
            charCountPaint.setTextSize(charCountTextSize);
        }
        invalidate();
    }

    public boolean isShowCharCount() {
        return showCharCount;
    }

    public void setShowCharCount(boolean showCharCount) {
        this.showCharCount = showCharCount;
        if (showCharCount && isMultiLine && charCountPaint == null) {
            charCountPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            charCountPaint.setColor(charCountTextColor);
            charCountPaint.setTextSize(charCountTextSize);
            charCountPaint.setTextAlign(Paint.Align.RIGHT);
        }
        invalidate();
    }

    private void init(Context context, AttributeSet attrs) {
        android.content.res.TypedArray a = context.obtainStyledAttributes(attrs, R.styleable.CustomEditText);
        imgCloseButton = ContextCompat.getDrawable(getContext(), android.R.drawable.ic_menu_close_clear_cancel);
        cPadding = a.getDimensionPixelSize(R.styleable.CustomEditText_android_padding, -1);
        cPaddingLeft = a.getDimensionPixelSize(R.styleable.CustomEditText_android_paddingLeft, DEFAULT_PADDING);
        cPaddingTop = a.getDimensionPixelSize(R.styleable.CustomEditText_android_paddingTop, DEFAULT_PADDING);
        cPaddingRight = a.getDimensionPixelSize(R.styleable.CustomEditText_android_paddingRight, DEFAULT_PADDING);
        cPaddingBottom = a.getDimensionPixelSize(R.styleable.CustomEditText_android_paddingBottom, DEFAULT_PADDING);
        isClearIconVisible = a.getBoolean(R.styleable.CustomEditText_edt_setClearIconVisible, false);
        boolean isBorderView = a.getBoolean(R.styleable.CustomEditText_edt_setBorderView, false);
        int mNormalColor = a.getColor(R.styleable.CustomEditText_edt_setBorderColor, DEFAULTCOLOR);
        cursorDrawable = a.getResourceId(R.styleable.CustomEditText_edt_cursor, 0);
        mBackgroundColor = a.getColor(R.styleable.CustomEditText_edt_setBackgroundColor, Color.TRANSPARENT);
        mStrokeWidth = a.getDimension(R.styleable.CustomEditText_edt_setStrokeWidth, mStrokeWidth);
        hideShowIconTint = a.getColor(R.styleable.CustomEditText_edt_hideShowPasswordIconTint, DEFAULTCOLOR);
        clearIconTint = a.getColor(R.styleable.CustomEditText_edt_clearIconTint, DEFAULTCOLOR);
        this.font = a.getString(R.styleable.CustomEditText_edt_setFont);
        mPrefix = a.getString(R.styleable.CustomEditText_edt_setPrefix);
        minLength = a.getString(R.styleable.CustomEditText_edt_minLength);
        regexp = a.getString(R.styleable.CustomEditText_edt_regexp);
        prefixTextColor = a.getColor(R.styleable.CustomEditText_edt_setPrefixTextColor, 0);
        mCornerRadius = a.getDimension(R.styleable.CustomEditText_edt_setCornerRadius, 1f);
        
        // 多行输入相关属性
        isMultiLine = a.getBoolean(R.styleable.CustomEditText_edt_multiLine, false);
        maxCharCount = a.getInteger(R.styleable.CustomEditText_edt_maxCharCount, 60);
        charCountTextColor = a.getColor(R.styleable.CustomEditText_edt_charCountTextColor, Color.GRAY);
        charCountTextSize = a.getDimension(R.styleable.CustomEditText_edt_charCountTextSize, 12f);
        showCharCount = a.getBoolean(R.styleable.CustomEditText_edt_showCharCount, true); // 新增：控制是否显示字数统计

        if (isBorderView) {
            setBackGroundOfLayout(getShapeBackground(mNormalColor));
        } else {
            // 默认无边框、无下划线
            setBackground(null);
        }
        
        if (cursorDrawable != 0) {
            try {
                @SuppressWarnings("SoonBlockedPrivateApi")
                java.lang.reflect.Field f = TextView.class.getDeclaredField("mCursorDrawableRes");
                f.setAccessible(true);
                f.set(this, cursorDrawable);
            } catch (Exception ignored) {
            }
        }
        
        if (getInputType() == TYPE_TEXT_VARIATION_PASSWORD || getInputType() == TYPE_NUMBER_VARIATION_PASSWORD) {
            isPassword = true;
            this.setMaxLines(1);
        } else if (getInputType() == EditorInfo.TYPE_CLASS_PHONE) {
            this.setKeyListener(DigitsKeyListener.getInstance("0123456789"));
        }
        
        // 初始化多行输入
        if (isMultiLine) {
            setMaxLines(5);
            setMinLines(3);
            setGravity(android.view.Gravity.TOP | android.view.Gravity.START);
            setFilters(new android.text.InputFilter[]{new android.text.InputFilter.LengthFilter(maxCharCount)});
            
            // 为字数统计预留空间，避免被覆盖
            if (showCharCount) {
                int currentPaddingRight = getPaddingRight();
                int charCountWidth = (int) (charCountTextSize * 4); // 估算字数统计文字宽度
                setPadding(getPaddingLeft(), getPaddingTop(), 
                          currentPaddingRight + charCountWidth + 20, getPaddingBottom());
            }
        }
        
        if (!isPassword && isClearIconVisible) {
            handleClearButton();
        }

        if (mPrefix != null && mPrefix.length() > 0) {
            calculatePrefix();
        }

        if (isPassword) {
            if (!TextUtils.isEmpty(getText())) {
                showPasswordVisibilityIndicator(true);
            } else {
                showPasswordVisibilityIndicator(false);
            }
        }
        
        // 初始化字数统计画笔
        if (isMultiLine && showCharCount) { // 新增：控制是否显示字数统计
            charCountPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            charCountPaint.setColor(charCountTextColor);
            charCountPaint.setTextSize(charCountTextSize);
            charCountPaint.setTextAlign(Paint.Align.RIGHT);
        }
        
        setOnTouchListener(new OnTouchListener() {
            @Override
            public boolean onTouch(View view, MotionEvent event) {
                CustomEditText editText = CustomEditText.this;
                if (editText.getCompoundDrawables()[2] == null)
                    return false;
                if (event.getAction() != MotionEvent.ACTION_UP)
                    return false;
                if (isPassword) {
                    int width = (drawableEnd == null) ? 0 : drawableEnd.getIntrinsicWidth();
                    if (event.getX() > editText.getWidth() - editText.getPaddingRight() - width) {
                        togglePasswordVisibility();
                        event.setAction(MotionEvent.ACTION_CANCEL);
                    }
                } else if (isClearIconVisible) {
                    int width = (imgCloseButton == null) ? 0 : imgCloseButton.getIntrinsicWidth();
                    if (event.getX() > editText.getWidth() - editText.getPaddingRight() - width) {
                        editText.setText("");
                        CustomEditText.this.handleClearButton();
                    }
                } else {
                    if (event.getAction() == MotionEvent.ACTION_UP) {
                        if (event.getRawX() >= (editText.getRight() - editText.getCompoundDrawables()[DRAWABLE_RIGHT].getBounds().width())) {
                            editText.setFocusableInTouchMode(false);
                            if (clickListener != null) clickListener.onRightClick();
                            return false;
                        } else {
                            editText.setFocusableInTouchMode(true);
                        }
                    }
                }
                return false;
            }
        });

        setOnFocusChangeListener(new OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                CustomEditText editText = CustomEditText.this;
                if (minLength != null) {
                    if (!hasFocus) {
                        if (editText.getText().toString().trim().length() > 0) {
                            if (editText.getText().toString().trim().length() < Integer.parseInt(minLength)) {
                                CommonUtils.showAlertDialog(context, "Alert", context.getString(R.string.error_min_value, minLength));
                            }
                        }
                    }
                }
                if (regexp != null) {
                    if (!hasFocus) {
                        inputtext = editText.getEditableText().toString().trim();
                        if (editText.getText().toString().trim().length() > 0) {
                            if (!inputtext.matches(regexp)) {
                                CommonUtils.showAlertDialog(context, "Alert", context.getString(R.string.error_regex));
                            }
                        }
                    }
                }
            }
        });

        setFont();
        a.recycle();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        if (mPrefix != null)
            calculatePrefix();
    }

    /**
     * This method is used for set font in edit text.
     */
    private void setFont() {
        if (font != null) {
            try {
                setTypeface(Typefaces.get(getContext(), font));
            } catch (Exception ignored) {
            }
        }
    }

    /**
     * This method is used to set the rectangle box on EditText
     */
    private void setBackGroundOfLayout(Drawable shape) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            setBackground(shape);
        } else {
            setBackgroundDrawable(shape);
        }
        padding(true);
    }

    public void setDrawableClickListener(DrawableClickListener listener) {
        this.clickListener = listener;
    }

    private void padding(boolean isRound) {
        int extraPadding;
        int extraPad;
        if (isRound) {
            extraPadding = 5;
            extraPad = 0;
        } else {
            extraPad = 5;
            extraPadding = 0;
        }
        if (cPadding != -1) {
            super.setPadding(cPadding + extraPadding, cPadding, cPadding, cPadding + extraPad);
        } else {
            super.setPadding(cPaddingLeft + extraPadding, cPaddingTop, cPaddingRight, cPaddingBottom + extraPad);
        }
    }

    /**
     * This method is used to draw the rectangle border view with color
     */
    @SuppressLint("WrongConstant")
    private Drawable getShapeBackground(@ColorInt int color) {
        GradientDrawable shape = new GradientDrawable();
        shape.setShape(GradientDrawable.RECTANGLE);
        shape.setCornerRadius(mCornerRadius);
        shape.setColor(mBackgroundColor);
        shape.setStroke((int) mStrokeWidth, color);
        return shape;
    }

    @SuppressLint("DrawAllocation")
    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        if (mPrefix != null) {
            String prefix = mPrefix;
            Paint myPaint = null;
            if (prefixTextColor != 0) {
                myPaint = new Paint(Paint.ANTI_ALIAS_FLAG | Paint.DITHER_FLAG);
                myPaint.setColor(prefixTextColor);
                myPaint.setTextAlign(Paint.Align.LEFT);
                myPaint.setTextSize(getTextSize());
            }
            canvas.drawText(prefix, mOriginalLeftPadding, getLineBounds(0, null), myPaint != null ? myPaint : getPaint());
        }
        
        // 绘制字数统计
        if (isMultiLine && showCharCount && charCountPaint != null) {
            int currentLength = getText().length();
            String charCountText = currentLength + "/" + maxCharCount;
            
            // 计算绘制位置（右下角，确保不被文本覆盖）
            Rect bounds = new Rect();
            charCountPaint.getTextBounds(charCountText, 0, charCountText.length(), bounds);
            
            // 计算安全的绘制位置
            float x = getWidth() - 20; // 距离右边缘20px
            float y = getHeight() - 15; // 距离底边缘15px
            
            // 如果字数超过限制，改变颜色
            if (currentLength > maxCharCount) {
                charCountPaint.setColor(Color.RED);
            } else {
                charCountPaint.setColor(charCountTextColor);
            }
            
            // 绘制背景矩形，确保文字清晰可见
            Paint bgPaint = new Paint();
            bgPaint.setColor(Color.WHITE);
            bgPaint.setAlpha(200);
            canvas.drawRect(x - bounds.width() - 10, y - bounds.height() - 5, 
                           x + 5, y + 5, bgPaint);
            
            canvas.drawText(charCountText, x, y, charCountPaint);
        }
    }

    @SuppressLint("NewApi")
    private void handleClearButton() {
        if (isClearIconVisible) {
            DrawableCompat.setTint(imgCloseButton, clearIconTint);
            imgCloseButton.setBounds(0, 0, 43, 43);
            if (Objects.requireNonNull(this.getText()).length() == 0) {
                this.setCompoundDrawables(this.getCompoundDrawables()[0], this.getCompoundDrawables()[1], null, this.getCompoundDrawables()[3]);
            } else {
                this.setCompoundDrawables(this.getCompoundDrawables()[0], this.getCompoundDrawables()[1], imgCloseButton, this.getCompoundDrawables()[3]);
            }
        }
    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {
        try {
            if (isPassword) {
                if (s.length() > 0) {
                    showPasswordVisibilityIndicator(true);
                } else {
                    isShowingPassword = false;
                    maskPassword();
                    showPasswordVisibilityIndicator(false);
                }
            } else if (isClearIconVisible)
                this.handleClearButton();

            // 多行输入时重绘字数统计
            if (isMultiLine) {
                invalidate();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void showPasswordVisibilityIndicator(boolean show) {
        if (show) {
            Drawable original = isShowingPassword ?
                    ContextCompat.getDrawable(getContext(), R.drawable.ic_visibility_on) :
                    ContextCompat.getDrawable(getContext(), R.drawable.ic_visibility_off);
            if (original != null) {
                original.mutate();
                DrawableCompat.setTint(original, hideShowIconTint);
                original.setBounds(0, 0, 43, 43);
                drawableEnd = original;
                this.setCompoundDrawables(this.getCompoundDrawables()[0], this.getCompoundDrawables()[1], original, this.getCompoundDrawables()[3]);
            }
        } else {
            this.setCompoundDrawables(this.getCompoundDrawables()[0], this.getCompoundDrawables()[1], null, this.getCompoundDrawables()[3]);
        }
    }

    // make it visible
    private void unmaskPassword() {
        setTransformationMethod(null);
    }

    // hide it
    private void maskPassword() {
        setTransformationMethod(PasswordTransformationMethod.getInstance());
    }

    private int getThemeAccentColor() {
        int colorAttr;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            colorAttr = android.R.attr.textColor;
        } else {
            colorAttr = getContext().getResources().getIdentifier("colorAccent", "attr", getContext().getPackageName());
        }
        TypedValue outValue = new TypedValue();
        getContext().getTheme().resolveAttribute(colorAttr, outValue, true);
        return outValue.data;
    }

    private void togglePasswordVisibility() {
        // Store the selection
        int selectionStart = this.getSelectionStart();
        int selectionEnd = this.getSelectionEnd();
        // Set transformation method to show/hide password
        if (isShowingPassword) {
            maskPassword();
        } else {
            unmaskPassword();
        }
        // Restore selection
        this.setSelection(selectionStart, selectionEnd);
        // Toggle flag and show indicator
        isShowingPassword = !isShowingPassword;
        showPasswordVisibilityIndicator(true);
    }

    private void calculatePrefix() {
        if (mOriginalLeftPadding == -1f) {
            String prefix = mPrefix;
            float[] widths = new float[prefix.length()];
            getPaint().getTextWidths(prefix, widths);
            float textWidth = 0f;
            for (float w : widths) {
                textWidth += w;
            }
            mOriginalLeftPadding = getCompoundPaddingLeft();
            setPadding((int) (textWidth + mOriginalLeftPadding),
                    getPaddingRight(), getPaddingTop(),
                    getPaddingBottom());
        }
    }

    public void setPrefixTextColor(int prefixTextColor) {
        this.prefixTextColor = prefixTextColor;
        invalidate();
    }
} 