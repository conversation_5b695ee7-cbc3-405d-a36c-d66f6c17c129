# CustomInputBox3 多行输入功能总结

## 新增功能

### 1. 多行输入支持
- 支持多行文本输入，适合评论、简介等场景
- 自动设置最小3行，最大5行
- 支持自定义高度

### 2. 字数统计显示
- 右下角实时显示当前字数/最大字数
- 格式：`0/60`、`25/100` 等
- 超过字数限制时，统计文字变为红色警告
- 支持自定义字数限制、文字颜色和大小
- 支持控制字数统计的显示/隐藏
- 字数统计有白色半透明背景保护，确保不被文本覆盖

### 3. 新增自定义属性

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `edt_multiLine` | boolean | false | 是否启用多行输入模式 |
| `edt_maxCharCount` | integer | 60 | 最大字符数限制 |
| `edt_charCountTextColor` | color | #808080 | 字数统计文本颜色 |
| `edt_charCountTextSize` | dimension | 12sp | 字数统计文本大小 |
| `edt_showCharCount` | boolean | true | 是否显示字数统计 |

## 使用示例

### XML布局中使用

```xml
<!-- 显示字数统计的多行输入框 -->
<com.wkb.custominputbox3.CustomEditText
    android:layout_width="match_parent"
    android:layout_height="120dp"
    android:hint="请输入评论内容..."
    android:inputType="textMultiLine"
    app:edt_multiLine="true"
    app:edt_maxCharCount="60"
    app:edt_charCountTextColor="#666666"
    app:edt_charCountTextSize="12sp"
    app:edt_showCharCount="true"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#FF9800"
    app:edt_setCornerRadius="8dp" />

<!-- 隐藏字数统计的多行输入框 -->
<com.wkb.custominputbox3.CustomEditText
    android:layout_width="match_parent"
    android:layout_height="80dp"
    android:hint="字数统计已隐藏..."
    android:inputType="textMultiLine"
    app:edt_multiLine="true"
    app:edt_maxCharCount="50"
    app:edt_showCharCount="false"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#9C27B0"
    app:edt_setCornerRadius="8dp" />
```

### Java代码中使用

```java
// 动态设置多行输入属性
CustomEditText multiLineEditText = findViewById(R.id.multiLineEditText);
multiLineEditText.setMultiLine(true);
multiLineEditText.setMaxCharCount(100);
multiLineEditText.setCharCountTextColor(Color.GRAY);
multiLineEditText.setCharCountTextSize(12f);
multiLineEditText.setShowCharCount(true); // 控制是否显示字数统计

// 添加字数监听
multiLineEditText.addTextChangedListener(new TextWatcher() {
    @Override
    public void afterTextChanged(Editable s) {
        if (s.length() > 100) {
            Toast.makeText(context, "已达到最大字数限制", Toast.LENGTH_SHORT).show();
        }
    }
    // ... 其他方法
});
```

## 技术实现

### 1. 核心类修改
- `CustomEditText.java`: 添加多行输入相关属性和方法
- `attrs.xml`: 新增自定义属性定义

### 2. 关键功能实现
- **字数统计绘制**: 在 `onDraw()` 方法中绘制字数统计文字
- **实时更新**: 在 `onTextChanged()` 中调用 `invalidate()` 重绘
- **颜色变化**: 超过字数限制时自动变为红色
- **字符限制**: 使用 `InputFilter.LengthFilter` 限制输入长度
- **背景保护**: 字数统计有白色半透明背景，确保不被文本覆盖
- **显示控制**: 支持通过属性控制字数统计的显示/隐藏

### 3. 示例展示
- `ExampleActivity3.java`: 展示多行输入框的使用
- `activity_example3.xml`: 包含两个不同配置的多行输入框示例

## 特性说明

1. **自动布局**: 多行输入框自动设置合适的行数和对齐方式
2. **实时反馈**: 字数统计实时更新，提供即时反馈
3. **视觉警告**: 超过限制时红色提示，增强用户体验
4. **灵活配置**: 支持自定义字数限制、颜色、大小等
5. **兼容性**: 与现有功能完全兼容，不影响其他特性
6. **显示控制**: 支持控制字数统计的显示/隐藏
7. **背景保护**: 字数统计有背景保护，确保在任何情况下都清晰可见
8. **滚动支持**: ExampleActivity3使用ScrollView，支持内容滚动

## 测试验证

项目已成功编译，可以在 `ExampleActivity3` 中测试多行输入功能：
1. 启动应用
2. 点击 "Try Java Version (CustomInputBox3)" 按钮
3. 查看并测试两个多行输入框示例
4. 验证字数统计显示和限制功能

## 注意事项

- 多行输入框会自动设置最大字符数限制
- 字数统计显示在右下角，不会影响文本输入
- 超过字数限制时，统计文字变为红色，但不会阻止输入
- 建议在代码中添加额外的字数验证逻辑
- 字数统计有白色半透明背景保护，确保在任何情况下都清晰可见
- 可以通过`edt_showCharCount`属性控制字数统计的显示/隐藏
- 当输入框高度较小时，字数统计仍能正常显示且不被覆盖 