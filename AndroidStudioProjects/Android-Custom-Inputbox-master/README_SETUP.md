# Android Studio 项目设置指南

## 解决 "Couldn't get post build model" 错误

如果你遇到 `Error running 'app' Couldn't get post build model. Module: CustomInputBox.app.main Variant: debug` 错误，请按照以下步骤操作：

### 步骤 1: 清理项目
```bash
./gradlew clean
```

### 步骤 2: 在 Android Studio 中重新导入项目
1. 关闭 Android Studio
2. 删除项目根目录下的 `.idea` 文件夹（如果存在）
3. 重新打开 Android Studio
4. 选择 "Open an existing Android Studio project"
5. 选择项目根目录 `Android-Custom-Inputbox-master`
6. 等待 Gradle 同步完成

### 步骤 3: 如果仍有问题，尝试以下操作
1. 在 Android Studio 中：
   - File → Invalidate Caches and Restart → Invalidate and Restart
2. 或者在终端中：
   ```bash
   ./gradlew clean
   ./gradlew build
   ```

### 步骤 4: 运行应用
- 确保有模拟器运行或设备连接
- 点击 Run 按钮或使用快捷键 Ctrl+R (Windows/Linux) 或 Cmd+R (Mac)

## 项目配置说明

### 已修复的问题
- ✅ 移除了弃用的 `kotlin-android-extensions` 插件
- ✅ 启用了 View Binding
- ✅ 更新了 Android Gradle Plugin 到 7.2.2
- ✅ 更新了 Kotlin 版本到 1.7.10
- ✅ 修复了 Android 12+ 兼容性问题
- ✅ 解决了编译错误

### 当前配置
- **compileSdk**: 32
- **minSdk**: 21
- **targetSdk**: 32
- **Android Gradle Plugin**: 7.2.2
- **Kotlin**: 1.7.10
- **Gradle**: 7.3.3

## 故障排除

如果遇到其他问题：

1. **同步失败**: 
   - 检查网络连接
   - 尝试使用 VPN（如果在中国）

2. **构建失败**:
   ```bash
   ./gradlew clean
   ./gradlew build --stacktrace
   ```

3. **模拟器问题**:
   - 确保 AVD 已创建并运行
   - 检查 AVD 的 API 级别是否兼容（推荐 API 28+）

## 联系支持
如果问题仍然存在，请提供：
- Android Studio 版本
- 错误日志的完整信息
- 操作系统信息
