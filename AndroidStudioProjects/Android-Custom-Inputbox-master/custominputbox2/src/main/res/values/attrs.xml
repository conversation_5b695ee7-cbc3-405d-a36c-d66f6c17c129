<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="CustomEditText">
        <attr name="android:inputType" />
        <attr name="android:padding" />
        <attr name="android:paddingLeft" />
        <attr name="android:paddingRight" />
        <attr name="android:paddingTop" />
        <attr name="android:paddingBottom" />
        <attr name="android:maxLength" />
        <attr name="edt_cursor" format="reference" />
        <attr name="edt_setBackgroundColor" format="color" />
        <attr name="edt_background" format="reference" />
        <attr name="edt_setBorderColor" format="color" />
        <attr name="edt_setBorderView" format="boolean" />
        <attr name="edt_setFont" format="string" />
        <attr name="edt_setStrokeWidth" format="dimension" />
        <attr name="edt_setCornerRadius" format="dimension" />
        <attr name="edt_setClearIconVisible" format="boolean" />
        <attr name="edt_clearIconTint" format="color" />
        <attr name="edt_hideShowPasswordIconTint" format="color" />
        <attr name="edt_setPrefix" format="string" />
        <attr name="edt_setPrefixTextColor" format="color" />
        <attr name="edt_minLength" format="string" />
        <attr name="edt_regexp" format="string" />
        <attr name="edt_pattern" format="string" />
        <attr name="edt_specialChar" format="string" />
        <attr name="edt_showPatternAsHint" format="boolean" />
    </declare-styleable>

    <attr name="keyboardType">
        <enum name="integer" value="0"/>
        <enum name="decimal" value="1"/>
        <enum name="fingerprint" value="2"/>
        <enum name="custom" value="3"/>
        <enum name="four_columns" value="4"/>
    </attr>

    <attr name="keyWidth" format="dimension">
        <enum name="match_parent" value="-1"/>
    </attr>

    <attr name="keyHeight" format="dimension">
        <enum name="match_parent" value="-1"/>
    </attr>

    <declare-styleable name="NumberKeyboard">
        <attr name="keyboardType"/>
        <attr name="keyWidth"/>
        <attr name="keyHeight"/>
        <attr name="keyPadding" format="dimension"/>
        <attr name="numberKeyBackground" format="reference"/>
        <attr name="numberKeyTextColor" format="reference"/>
        <attr name="leftAuxTextColor" format="reference"/>
        <attr name="leftAuxBtnIcon" format="reference"/>
        <attr name="leftAuxBtnBackground" format="reference"/>
        <attr name="rightAuxBtnIcon" format="reference"/>
        <attr name="rightAuxBtnBackground" format="reference"/>
        <attr name="layout" format="reference"/>
    </declare-styleable>


</resources>