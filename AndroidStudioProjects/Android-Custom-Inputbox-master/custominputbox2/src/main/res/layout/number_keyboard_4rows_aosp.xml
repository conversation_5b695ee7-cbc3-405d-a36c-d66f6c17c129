<?xml version="1.0" encoding="utf-8"?>

<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!--<com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/keyOkContainer"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key1Container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">-->

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="45dp"
            android:id="@+id/keyOkContainer"
            app:layout_constraintBottom_toTopOf="@+id/key1Container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:paddingTop="5dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/keyOk"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:clickable="true"
            android:includeFontPadding="false"
            android:lineSpacingExtra="0dp"
            android:textSize="23sp"
            android:padding="5dp"
            android:textColor="#00AED9"
            android:fontFamily="@font/roboto_medium"
            android:text="@string/ok"/>

        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#707070"
            android:alpha="0.4"
            android:layout_marginStart="35dp"
            android:layout_marginEnd="35dp"

            android:layout_gravity="bottom|center_horizontal" />

        </FrameLayout>

    <!--</com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>-->

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key1Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key4Container"
        app:layout_constraintEnd_toStartOf="@+id/key2Container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/keyOkContainer">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/key1"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/one"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key2Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key5Container"
        app:layout_constraintEnd_toStartOf="@+id/key3Container"
        app:layout_constraintStart_toEndOf="@+id/key1Container"
        app:layout_constraintTop_toBottomOf="@id/keyOkContainer">

        <TextView
            android:id="@+id/key2"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/two"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key3Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key6Container"
        app:layout_constraintEnd_toStartOf="@+id/modifier1Container"
        app:layout_constraintStart_toEndOf="@+id/key2Container"
        app:layout_constraintTop_toBottomOf="@id/keyOkContainer">

        <TextView
            android:id="@+id/key3"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/three"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/modifier1Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key6Container"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/key3Container"
        app:layout_constraintTop_toBottomOf="@id/keyOkContainer">

        <TextView
            android:id="@+id/keyModifier1"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:visibility="invisible"
            android:text="@string/minus"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key4Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key7Container"
        app:layout_constraintEnd_toStartOf="@+id/key5Container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/key1Container">

        <TextView
            android:id="@+id/key4"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/four"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>


    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key5Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key8Container"
        app:layout_constraintEnd_toStartOf="@+id/key6Container"
        app:layout_constraintStart_toEndOf="@+id/key4Container"
        app:layout_constraintTop_toBottomOf="@+id/key2Container">

        <TextView
            android:id="@+id/key5"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/five"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key6Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key9Container"
        app:layout_constraintEnd_toStartOf="@+id/modifier2Container"
        app:layout_constraintStart_toEndOf="@+id/key5Container"
        app:layout_constraintTop_toBottomOf="@+id/key3Container">

        <TextView
            android:id="@+id/key6"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/six"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/modifier2Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key9Container"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/key6Container"
        app:layout_constraintTop_toBottomOf="@+id/key3Container">

        <TextView
            android:id="@+id/keyModifier2"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/comma"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key7Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/leftAuxBtnContainer"
        app:layout_constraintEnd_toStartOf="@+id/key8Container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/key4Container">

        <TextView
            android:id="@+id/key7"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/seven"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>


    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key8Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/key0Container"
        app:layout_constraintEnd_toStartOf="@+id/key9Container"
        app:layout_constraintStart_toEndOf="@+id/key7Container"
        app:layout_constraintTop_toBottomOf="@+id/key5Container">

        <TextView
            android:id="@+id/key8"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/eight"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key9Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/rightAuxBtnContainer"
        app:layout_constraintEnd_toStartOf="@+id/modifier3Container"
        app:layout_constraintStart_toEndOf="@+id/key8Container"
        app:layout_constraintTop_toBottomOf="@+id/key6Container">

        <TextView
            android:id="@+id/key9"
            style="@style/keyNoBg"
            android:padding="0dp"
            android:text="@string/nine"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/modifier3Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toTopOf="@+id/rightAuxBtnContainer"
        app:layout_constraintEnd_toEndOf="parent"
        android:visibility="gone"
        app:layout_constraintStart_toEndOf="@+id/key9Container"
        app:layout_constraintTop_toBottomOf="@+id/key6Container">

        <ImageView
            android:id="@+id/buttonModifier3"
            style="@style/keyNoBg"
            android:scaleType="center"
            app:srcCompat="@drawable/ic_backspace"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/leftAuxBtnContainer"
        style="@style/keyContainer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/key0Container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/key7Container">

        <!--<ImageView
            android:id="@+id/leftAuxBtn"
            style="@style/keyNoBg"
            android:scaleType="center"
            app:srcCompat="@drawable/ic_fingerprint"/>-->
        <TextView
            android:id="@+id/keyComma"
            style="@style/keyNoBg"
            android:text="@string/comma"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>


    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/key0Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/rightAuxBtnContainer"
        app:layout_constraintStart_toEndOf="@+id/leftAuxBtnContainer"
        app:layout_constraintTop_toBottomOf="@+id/key8Container">

        <TextView
            android:id="@+id/key0"
            style="@style/keyNoBg"
            android:text="@string/zero"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/rightAuxBtnContainer"
        style="@style/keyContainer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/modifier4Container"
        app:layout_constraintStart_toEndOf="@+id/key0Container"
        app:layout_constraintTop_toBottomOf="@+id/key9Container">

        <ImageView
            android:id="@+id/rightAuxBtn"
            style="@style/keyNoBg"
            android:scaleType="center"
            app:srcCompat="@drawable/ic_backspace"/>
        <!--<TextView
            android:id="@+id/keyDot"
            style="@style/keyNoBg"
            android:text="@string/dot"/>-->

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>

    <com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout
        android:id="@+id/modifier4Container"
        style="@style/keyContainer"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/rightAuxBtnContainer"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@+id/key9Container">

        <ImageView
            android:id="@+id/buttonModifier4"
            style="@style/keyNoBg"
            android:scaleType="center"
            app:srcCompat="@drawable/ic_check_circle"/>

    </com.wkb.custominputbox2.numberkeyboard.SquareFrameLayout>
</merge>