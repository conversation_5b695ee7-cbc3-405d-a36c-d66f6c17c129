# MultiLineEditText 控件总结

## 概述

**MultiLineEditText已经完全替代了CustomEditText！** 为了解决字数统计被覆盖的问题，我们创建了一个全新的`MultiLineEditText`控件，它使用FrameLayout布局来实现更好的字数统计显示效果，并且集成了CustomEditText的所有功能。

## 核心特性

### 1. 默认设置
- **默认单行模式**：默认不启用多行输入
- **默认不显示字数统计**：需要时可通过属性或代码启用
- **无系统默认横线**：去掉了EditText默认的底部横线，但保留hint显示功能

### 2. 多行模式
- 字数统计独立显示在右下角
- 不随文本滚动，始终保持在固定位置
- 文本可以上下滚动，字数统计不动
- 适合评论、简介等长文本输入场景

### 3. 单行模式
- 文本和数字保持左右结构
- 数字在右侧，文本在左侧
- 文本过长时不会覆盖右侧数字
- **智能布局**：以右侧控件宽度为准，剩余空间给输入框
- 适合短文本输入场景

### 4. 字数统计功能
- 实时显示当前字数/最大字数
- 超过字数限制时，统计文字变为红色，并限制输入
- 透明显示，无背景干扰
- 支持自定义字数限制、文字颜色和大小
- 支持控制字数统计的显示/隐藏
- TextView不接收触摸事件，确保EditText可以正常获取焦点
- 重写了dispatchTouchEvent方法，确保单行模式下触摸事件正确传递

### 5. CustomEditText功能集成
- **前缀功能**：支持设置前缀文本和颜色
- **清除按钮**：支持显示/隐藏清除按钮
- **边框样式**：支持自定义边框颜色、圆角、背景色
- **字体设置**：支持自定义字体
- **验证功能**：支持最小长度和正则表达式验证
- **触摸事件优化**：确保EditText可以正常接收输入

## 技术实现

### 布局结构
```
FrameLayout (MultiLineEditText)
├── AppCompatEditText (文本输入区域，集成所有CustomEditText功能)
└── TextView (字数统计显示)
```

### 布局策略
- **多行模式**：EditText占据整个区域，TextView固定在右下角
- **单行模式**：EditText占据左侧（MATCH_PARENT宽度，右侧预留80dp），TextView固定在右侧
- **隐藏模式**：只显示EditText，不显示TextView
- **触摸事件处理**：TextView设置为不可点击、不可获取焦点，确保EditText可以正常接收触摸事件

### 关键方法
- `setupLayout()`: 根据模式设置不同的布局参数
- `updateCharCount()`: 更新字数统计显示
- `createCharCountTextView()`: 创建字数统计TextView
- `createEditText()`: 创建内部EditText（集成CustomEditText功能）
- `dispatchTouchEvent()`: 重写触摸事件分发，确保EditText可以正常接收输入
- `onTouchEvent()`: 重写触摸事件处理
- `onInterceptTouchEvent()`: 不拦截触摸事件，让子视图处理
- `initPrefixDrawing()`: 初始化前缀绘制
- `drawPrefix()`: 绘制前缀文本
- `setupClearButton()`: 设置清除按钮
- `handleClearButton()`: 处理清除按钮显示/隐藏
- `TextWatcher.afterTextChanged()`: 限制输入长度，防止超出最大字数

## 使用方法

### XML布局中使用

```xml
<!-- 默认单行模式（推荐） -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:hint="输入文本"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#2196F3"
    app:edt_setCornerRadius="8dp" />

<!-- 启用字数统计的单行模式 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:hint="输入文本"
    app:edt_showCharCount="true"
    app:edt_maxCharCount="50"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#4CAF50"
    app:edt_setCornerRadius="8dp" />

<!-- 多行模式：字数统计在右下角 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="120dp"
    android:hint="请输入评论内容..."
    android:inputType="textMultiLine"
    app:edt_multiLine="true"
    app:edt_maxCharCount="60"
    app:edt_charCountTextColor="#666666"
    app:edt_charCountTextSize="12sp"
    app:edt_showCharCount="true"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#FF9800"
    app:edt_setCornerRadius="8dp" />

<!-- 单行模式：字数统计在右侧 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:hint="请输入简介..."
    android:inputType="text"
    app:edt_multiLine="false"
    app:edt_maxCharCount="100"
    app:edt_charCountTextColor="#2196F3"
    app:edt_charCountTextSize="14sp"
    app:edt_showCharCount="true"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#4CAF50"
    app:edt_setCornerRadius="8dp" />

<!-- 带前缀的输入框 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:hint="请输入金额"
    app:edt_setPrefix="$"
    app:edt_setPrefixTextColor="#4CAF50"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#4CAF50"
    app:edt_setCornerRadius="8dp" />

<!-- 带清除按钮的输入框 -->
<com.wkb.custominputbox3.MultiLineEditText
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:hint="带清除按钮的输入框"
    app:edt_setClearIconVisible="true"
    app:edt_clearIconTint="#F44336"
    app:edt_setBorderView="true"
    app:edt_setBorderColor="#2196F3"
    app:edt_setCornerRadius="8dp" />
```

### Java代码中使用

```java
MultiLineEditText multiLineEditText = findViewById(R.id.multiLineEditText);

// 设置多行模式
multiLineEditText.setMultiLine(true);
multiLineEditText.setMaxCharCount(100);
multiLineEditText.setCharCountTextColor(Color.GRAY);
multiLineEditText.setCharCountTextSize(12f);
multiLineEditText.setShowCharCount(true);

// 设置前缀
multiLineEditText.setPrefix("$");

// 设置清除按钮
multiLineEditText.setClearIconVisible(true);

// 获取内部EditText进行其他操作
AppCompatEditText editText = multiLineEditText.getEditText();
editText.addTextChangedListener(new TextWatcher() {
    @Override
    public void afterTextChanged(Editable s) {
        if (s.length() > 100) {
            Toast.makeText(context, "已达到最大字数限制", Toast.LENGTH_SHORT).show();
        }
    }
    // ... 其他方法
});
```

## 与CustomEditText的对比

| 特性 | CustomEditText | MultiLineEditText |
|------|----------------|-------------------|
| 字数统计位置 | 绘制在EditText内部 | 独立的TextView |
| 多行模式 | 字数统计可能被覆盖 | 字数统计固定在右下角 |
| 单行模式 | 字数统计可能被覆盖 | 文本和数字左右分离 |
| 布局方式 | 单一EditText | FrameLayout + EditText + TextView |
| 前缀功能 | ✅ | ✅ |
| 清除按钮 | ✅ | ✅ |
| 边框样式 | ✅ | ✅ |
| 字体设置 | ✅ | ✅ |
| 验证功能 | ✅ | ✅ |
| 触摸事件 | 正常 | 优化处理 |
| **推荐使用** | ❌ | ✅ |

## 优势

1. **解决覆盖问题**：字数统计不会被文本覆盖
2. **布局灵活**：支持多行和单行两种模式
3. **显示稳定**：字数统计位置固定，不随文本滚动
4. **功能完整**：集成了CustomEditText的所有功能
5. **易于使用**：API简单，使用方便
6. **触摸事件优化**：重写触摸事件处理，确保EditText可以正常接收输入
7. **完全替代**：可以完全替代CustomEditText，无需额外修改

## 注意事项

- **推荐使用MultiLineEditText**：MultiLineEditText已经完全替代CustomEditText
- 多行模式下，字数统计独立显示在右下角
- 单行模式下，文本和数字保持左右结构
- 字数统计透明显示，无背景干扰
- 可以通过`edt_showCharCount`属性控制字数统计的显示/隐藏
- **已修复焦点问题**：字数统计TextView不接收触摸事件，确保EditText可以正常获取焦点和输入
- **触摸事件优化**：重写了dispatchTouchEvent方法，确保单行模式下触摸事件正确传递给EditText
- **功能完整**：MultiLineEditText包含了CustomEditText的所有功能，可以直接替换使用

## 示例

查看 `app/src/main/java/com/wkb/custominputbox/ExampleActivity3.java` 和对应的布局文件来了解完整的使用示例，包括：
- 基础输入框示例
- 多行模式示例
- 单行模式示例
- 字数统计隐藏示例
- 带前缀的输入框
- 带清除按钮的输入框
- 密码输入框
- 不同配置的测试用例

## 迁移指南

如果你之前使用的是CustomEditText，现在可以：

1. **直接替换**：将XML中的`CustomEditText`替换为`MultiLineEditText`
2. **代码更新**：将Java代码中的`CustomEditText`类型替换为`MultiLineEditText`
3. **功能保持**：所有CustomEditText的功能都会正常工作
4. **额外功能**：获得更好的字数统计显示效果

```java
// 之前
CustomEditText editText = findViewById(R.id.editText);

// 现在
MultiLineEditText editText = findViewById(R.id.editText);
// 所有功能保持不变，额外获得更好的字数统计显示
``` 